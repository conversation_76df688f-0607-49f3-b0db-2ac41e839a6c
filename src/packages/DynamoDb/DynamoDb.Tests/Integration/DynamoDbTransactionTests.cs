using AMI.AmanoOne.Cloud.DynamoDb.Internal.Outbox;

namespace DynamoDb.Tests.Integration;

[UsesVerify]
[Collection(nameof(LocalStackAppFactoryCollection))]
public class DynamoDbTransactionTests : IDisposable
{
    private readonly LocalStackAppFactory _factory;
    private readonly IDynamoDbTransaction _transaction;
    private readonly IDynamoDbWrapper _wrapper;

    public DynamoDbTransactionTests(LocalStackAppFactory factory)
    {
        _factory = factory;
        _wrapper = factory.Services!.GetRequiredService<IDynamoDbWrapper>();
        _transaction = factory.Services!.CreateScope().ServiceProvider.GetRequiredService<IDynamoDbTransaction>();
    }

    [Fact]
    public async Task SaveChangesAsync_ShouldPersistModel()
    {
        var id = Guid.NewGuid().ToString();
        var newRecord = new SomeModel { Id = id, Name = "Johnny" };
        _transaction.Upsert(newRecord);

        await _transaction.SaveChangesAsync();

        var savedModel = await _transaction.GetByHash<SomeModel>(id);
        savedModel.Should().BeEquivalentTo(newRecord);
    }

    [Fact]
    public async Task SaveChangesAsync_ShouldPersistMultipleOfTheSameModel()
    {
        _transaction.Upsert(new SomeModel { Id = Guid.NewGuid().ToString(), Name = "Johnny" });
        _transaction.Upsert(new SomeModel { Id = Guid.NewGuid().ToString(), Name = "Test" });
        _transaction.Upsert(new SomeModel { Id = Guid.NewGuid().ToString(), Name = "Sammy" });

        await _transaction.SaveChangesAsync();

        var models = await _transaction.GetAll<SomeModel>();
        models.Should().HaveCount(3);
    }

    [Fact]
    public async Task Upsert_ShouldNotSaveAnything_WhenSaveChangesIsNotCalled()
    {
        var newRecord = new SomeModel { Id = Guid.NewGuid().ToString(), Name = "Test" };
        _transaction.Upsert(newRecord);

        var savedModel = await _transaction.GetAll<SomeModel>();
        savedModel.Should().HaveCount(0);
    }

    [Fact]
    public async Task Delete_ShouldDeleteItem_WhenSaveChangesIsCalled()
    {
        var newRecord = await SaveAnItemToDb();

        _transaction.Delete(newRecord);
        await _transaction.SaveChangesAsync();

        var models = await _transaction.GetAll<SomeModel>();
        models.Should().HaveCount(0);
    }

    [Fact]
    public async Task DeleteWithKey_ShouldDeleteItem_WhenSaveChangesIsCalled()
    {
        var newRecord = await SaveAnItemToDb();

        _transaction.Delete<SomeModel>(newRecord.Id);
        await _transaction.SaveChangesAsync();

        var models = await _transaction.GetAll<SomeModel>();
        models.Should().HaveCount(0);
    }

    [Fact]
    public async Task AddMessage_ShouldSaveOutboxMessage_WhenSaveChangesIsCalled()
    {
        var newRecord = await SaveAnItemToDb();

        _transaction.Upsert(newRecord);
        _transaction.AddMessage(new OutboxMessage
        {
            Topic = "TEST_TOPIC",
            Payload = new
            {
                MyValue = "TEST"
            }
        });
        await _transaction.SaveChangesAsync();

        var models = await _transaction.GetAll<SomeModel>();
        models.Should().HaveCount(1);

        var outboxMessages = await _transaction.GetAll<DbOutboxMessage>();
        outboxMessages.Should().HaveCount(1);
    }

    [Fact]
    public async Task GetBatch_ShouldReturnMultipleItems()
    {
        var listOfNewItems = new[]
        {
            new SomeModel { Id = Guid.NewGuid().ToString(), Name = "Test" },
            new SomeModel { Id = Guid.NewGuid().ToString(), Name = "Test2" },
            new SomeModel { Id = Guid.NewGuid().ToString(), Name = "Test3" },
        };
        foreach (var someModel in listOfNewItems)
        {
            await _wrapper.SaveAsync(someModel);
        }

        var wholeTable = await _transaction.GetAll<SomeModel>();
        wholeTable.Should().HaveCount(3);

        var models = await _transaction.GetBatch<SomeModel, string>(new List<string>
        {
            listOfNewItems.First().Id,
            listOfNewItems.Last().Id,
        });

        models.Should().HaveCount(2);
    }

    private async Task<SomeModel> SaveAnItemToDb()
    {
        var newRecord = new SomeModel { Id = Guid.NewGuid().ToString(), Name = "Test" };
        await _wrapper.SaveAsync(newRecord);
        var models = await _wrapper.GetAll<SomeModel>();
        models.Should().HaveCount(1);
        return newRecord;
    }

    public void Dispose()
    {
        _factory.QueuePublisher.Invocations.Clear();
        var data = _wrapper.GetAll<SomeModel>().GetAwaiter().GetResult();
        foreach (var model in data)
        {
            _wrapper.Delete<SomeModel>(model.Id).GetAwaiter().GetResult();
        }

        var outboxMessages = _wrapper.GetAll<DbOutboxMessage>().GetAwaiter().GetResult();
        foreach (var model in outboxMessages)
        {
            _wrapper.Delete<DbOutboxMessage>(model.Key).GetAwaiter().GetResult();
        }
    }
}

[DynamoDBTable("SomeModel")]
public class SomeModel
{
    public string Id { get; init; }
    public string Name { get; init; }
}