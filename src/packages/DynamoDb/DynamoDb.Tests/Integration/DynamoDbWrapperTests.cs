namespace DynamoDb.Tests.Integration;

[UsesVerify]
[Collection(nameof(LocalStackAppFactoryCollection))]
public class DynamoDbWrapperTests : IDisposable
{
    private readonly IDynamoDbWrapper _wrapper;

    public DynamoDbWrapperTests(LocalStackAppFactory factory)
    {
        _wrapper = factory.Services!.GetRequiredService<IDynamoDbWrapper>();
    }

    [Fact]
    public async Task SaveAsync_ShouldPersistModel()
    {
        var id = Guid.NewGuid().ToString();
        var newRecord = new TestModel { Id = id, Name = "Johnny" };
        await _wrapper.SaveAsync(newRecord);

        var savedModel = await _wrapper.GetByHash<TestModel>(id, CancellationToken.None);
        savedModel.Should().BeEquivalentTo(newRecord);
    }

    [Fact]
    public async Task GetAll_ShouldGetAllRecordsFromTheTable()
    {
        await _wrapper.SaveAsync(new TestModel { Id = Guid.NewGuid().ToString(), Name = "Johnny" });
        await _wrapper.SaveAsync(new TestModel { Id = Guid.NewGuid().ToString(), Name = "Dumb" });
        await _wrapper.SaveAsync(new TestModel { Id = Guid.NewGuid().ToString(), Name = "Wow" });

        var models = await _wrapper.GetAll<TestModel>();
        models.Should().HaveCount(3);
    }

    [Fact]
    public async Task GetBatch_ShouldGetRecordsByHash()
    {
        var items = new[]
        {
            new TestModel { Id = Guid.NewGuid().ToString(), Name = "Johnny" },
            new TestModel { Id = Guid.NewGuid().ToString(), Name = "Dumb" },
            new TestModel { Id = Guid.NewGuid().ToString(), Name = "Wow" },
        };
        foreach (var item in items)
        {
            await _wrapper.SaveAsync(item);
        }

        var models = await _wrapper.GetBatch<TestModel, string>(items.Skip(1).Select(x => x.Id).ToList());
        models.Should().HaveCount(2);
    }

    public void Dispose()
    {
        var data = _wrapper.GetAll<TestModel>().GetAwaiter().GetResult();
        foreach (var model in data)
        {
            _wrapper.Delete<TestModel>(model.Id).GetAwaiter().GetResult();
        }
    }
}

[DynamoDBTable("PackageTest")]
public class TestModel
{
    public string Id { get; init; }
    public string Name { get; init; }
}