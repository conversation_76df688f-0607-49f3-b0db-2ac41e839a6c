using AMI.AmanoOne.Cloud.DynamoDb.Internal.Outbox;
using AMI.AmanoOne.Cloud.EventQueuing;

namespace DynamoDb.Tests.Integration;

[UsesVerify]
[Collection(nameof(LocalStackAppFactoryCollection))]
public class OutboxMessageSenderTests : IDisposable
{
    private readonly LocalStackAppFactory _factory;
    private readonly IOutboxMessageSender _sender;
    private readonly IDynamoDbWrapper _wrapper;
    private readonly List<PublishQueueMessage> _publishQueueMessages = new();

    public OutboxMessageSenderTests(LocalStackAppFactory factory)
    {
        _factory = factory;
        _sender = factory.Services!.GetRequiredService<IOutboxMessageSender>();
        _wrapper = factory.Services!.GetRequiredService<IDynamoDbWrapper>();
        _factory.QueuePublisher.Setup(x => x.PublishAsync(Capture.In(_publishQueueMessages), It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);
    }

    [Fact]
    public async Task SendOutboxMessages_ShouldSendMessageToQueuePublisher()
    {
        var value = new OutboxMessage
        {
            Topic = "My_Topic",
            Payload = new
            {
                TestName = "John Smith"
            }
        };
        var dbOutboxMessage = new DbOutboxMessage
        {
            Key = value.Id,
            Created = DateTime.UtcNow,
            Payload = JsonSerializer.Serialize(value)
        };
        await _wrapper.SaveAsync(dbOutboxMessage);

        await _sender.SendOutboxMessages(new[] { dbOutboxMessage }, CancellationToken.None);

        _publishQueueMessages.Should().HaveCount(1);
        _publishQueueMessages.First().Should().BeEquivalentTo(new
        {
            MessageId = value.Id,
            Topic = value.Topic,
        });
    }

    [Fact]
    public async Task SendOutboxMessages_ShouldDeleteMessagesThatWereSent()
    {
        var dbOutboxMessage = new DbOutboxMessage
        {
            Key = Guid.NewGuid(),
            Created = DateTime.UtcNow,
            Payload = JsonSerializer.Serialize(new OutboxMessage
            {
                Topic = "My_Topic",
                Payload = new
                {
                    TestName = "John Smith"
                }
            })
        };
        await _wrapper.SaveAsync(dbOutboxMessage);

        await _sender.SendOutboxMessages(new[] { dbOutboxMessage }, CancellationToken.None);

        var outboxMessages = await _wrapper.GetAll<DbOutboxMessage>();
        outboxMessages.Should().HaveCount(0);
    }

    public void Dispose()
    {
        _factory.QueuePublisher.Invocations.Clear();
    }
}