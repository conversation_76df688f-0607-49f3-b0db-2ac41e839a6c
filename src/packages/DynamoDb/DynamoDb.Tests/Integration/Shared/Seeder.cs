using Microsoft.Extensions.Configuration;

namespace DynamoDb.Tests.Integration.Shared;

public interface ISeeder
{
    Task Initialize();
}

public class Seeder : ISeeder
{
    private readonly IAmazonDynamoDB _client;

    public Seeder(IConfiguration config)
    {
        var serviceUrl = config.GetValue<string>("AWS:ServiceURL");
        var amazonDynamoDB = new AmazonDynamoDBClient("123", "123",
            new AmazonDynamoDBConfig
            {
                RegionEndpoint = Amazon.RegionEndpoint.USEast2,
                UseHttp = true,
                ServiceURL = serviceUrl
            });

        _client = amazonDynamoDB;
    }

    public async Task Initialize()
    {
        await CreateTable("PackageTest", "Id", ScalarAttributeType.S, KeyType.HASH);
        await CreateTable("Outbox-Shared", "Key", ScalarAttributeType.S, KeyType.HASH);
        await CreateTable("SomeModel", "Id", ScalarAttributeType.S, KeyType.HASH);
    }

    private async Task CreateTable(string tableName, string keyName, ScalarAttributeType attributeType, KeyType keyType)
    {
        var tableCreateRequest = new CreateTableRequest
        {
            AttributeDefinitions = new List<AttributeDefinition>
            {
                new() { AttributeName = keyName, AttributeType = attributeType }
            },
            TableName = tableName,
            KeySchema = new List<KeySchemaElement>
            {
                new() { AttributeName = keyName, KeyType = keyType }
            },
            ProvisionedThroughput = new ProvisionedThroughput
            {
                ReadCapacityUnits = 5,
                WriteCapacityUnits = 6
            }
        };
        var response = await _client.CreateTableAsync(tableCreateRequest);
        response.HttpStatusCode.Should().Be(HttpStatusCode.OK, $"Failed to create Dynamo DB table {tableName} needed for test execution");
    }
}