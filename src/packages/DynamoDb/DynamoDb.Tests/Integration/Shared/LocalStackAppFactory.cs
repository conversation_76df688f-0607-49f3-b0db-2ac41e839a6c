using AMI.AmanoOne.Cloud.EventQueuing;
using Microsoft.Extensions.Configuration;
using Testcontainers.LocalStack;

// ReSharper disable HeuristicUnreachableCode
#pragma warning disable CS0162

namespace DynamoDb.Tests.Integration.Shared;

[CollectionDefinition(nameof(LocalStackAppFactoryCollection))]
public class LocalStackAppFactoryCollection : ICollectionFixture<LocalStackAppFactory>
{
}

public class LocalStackAppFactory : IAsyncLifetime
{
    public readonly Mock<IQueuePublisher> QueuePublisher = new();
    private int LocalstackPort => _localStackContainer?.GetMappedPublicPort(4566) ?? 4566;
    private string LocalstackUri => $"http://localhost:{LocalstackPort}";

    private readonly LocalStackContainer _localStackContainer = new LocalStackBuilder()
        .WithEnvironment("SERVICES", "dynamodb")
        .WithImage("localstack/localstack")
        .Build();

    public IServiceProvider? Services { get; private set; }

    async Task IAsyncLifetime.DisposeAsync()
    {
        await _localStackContainer?.StopAsync()!;
        await _localStackContainer.DisposeAsync();
    }

    public async Task InitializeAsync()
    {
        await _localStackContainer.StartAsync();

        SetupServices();

        var seeder = Services!.GetRequiredService<ISeeder>();
        await seeder.Initialize();
    }

    private void SetupServices()
    {
        var services = new ServiceCollection();
        var configuration = new ConfigurationBuilder().AddInMemoryCollection(
            new List<KeyValuePair<string, string>>
            {
                new("AWS:UseLocalConfig", "true"),
                new("AWS:ServiceURL", LocalstackUri)
            }).Build();
        services.AddDynamoDb(configuration);
        services.AddDynamoDbOutbox();
        services.AddSingleton<IConfiguration>(configuration);
        services.AddTransient<ISeeder, Seeder>();
        services.AddSingleton(QueuePublisher.Object);
        services.AddLogging();
        Services = services.BuildServiceProvider();
    }
}