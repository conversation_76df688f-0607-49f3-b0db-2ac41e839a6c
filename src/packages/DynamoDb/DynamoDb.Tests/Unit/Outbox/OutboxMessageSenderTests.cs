using AMI.AmanoOne.Cloud.DynamoDb.Internal.Outbox;
using AMI.AmanoOne.Cloud.EventQueuing;

namespace DynamoDb.Tests.Unit.Outbox;

public class OutboxMessageSenderTests
{
    private readonly OutboxMessageSender _sut;
    private readonly Mock<IQueuePublisher> _queuePublisher = new();
    private readonly Mock<IDynamoDBContext> _dynamoDBContext = new();
    private readonly Mock<ILogger<OutboxMessageSender>> _logger = new();

    public OutboxMessageSenderTests()
    {
        _sut = new OutboxMessageSender(_queuePublisher.Object, _dynamoDBContext.Object, _logger.Object);
    }

    [Fact]
    public async Task SendOutboxMessages_ShouldNotDeleteRecords_WhenSenderThrowsExceptions()
    {
        _queuePublisher.Setup(x => x.PublishAsync(It.IsAny<PublishQueueMessage>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("TEST"));

        await _sut.SendOutboxMessages(new[] { GenerateDbOutboxMessage() }, CancellationToken.None);

        _dynamoDBContext.VerifyNoOtherCalls();
    }

    [Fact]
    public async Task SendOutboxMessages_ShouldNotDeleteRecords_WhenSenderReturnsFalse()
    {
        _queuePublisher.Setup(x => x.PublishAsync(It.IsAny<PublishQueueMessage>(), It.IsAny<CancellationToken>())).ReturnsAsync(false);

        await _sut.SendOutboxMessages(new[] { GenerateDbOutboxMessage() }, CancellationToken.None);

        _dynamoDBContext.VerifyNoOtherCalls();
    }

    private static DbOutboxMessage GenerateDbOutboxMessage()
    {
        return new DbOutboxMessage { Payload = JsonSerializer.Serialize(new { }) };
    }
}