using AMI.AmanoOne.Cloud.DynamoDb.Internal.Outbox;
using AMI.AmanoOne.Cloud.DynamoDb.Internal.Outbox.Background;
using AMI.AmanoOne.Cloud.DynamoDb.Internal.Services;

namespace DynamoDb.Tests.Unit.Outbox.Background;

public class SenderBackgroundServiceTests
{
    private readonly SenderBackgroundService _sut;
    private readonly Mock<IOutboxMessageQueue> _queue = new();
    private readonly Mock<IOutboxMessageSender> _sender = new();
    private readonly Mock<IDelayWrapper> _delayWrapper = new();
    private readonly Mock<ILogger<SenderBackgroundService>> _logger = new();
    private readonly CancellationTokenSource _cts = CreateCancellationTokenSource();

    public SenderBackgroundServiceTests()
    {
        _queue.Setup(x => x.IsEmpty()).Returns(true);
        _sut = new SenderBackgroundService(_queue.Object, _sender.Object, _delayWrapper.Object, new DbOutboxOptions(), _logger.Object);
    }

    [Fact]
    public async Task ExecuteAsync_ShouldNotSendAnyMessages_WhenQueueIsEmpty()
    {
        _delayWrapper.Setup(x => x.SafeDelay(It.IsAny<TimeSpan>(), It.IsAny<CancellationToken>())).Callback(() => { _cts.Cancel(); });

        await _sut.StartAsync(_cts.Token);

        _sender.VerifyNoOtherCalls();
    }

    [Fact]
    public async Task ExecuteAsync_ShouldSendMessages_WhenQueueIsNotEmpty()
    {
        _queue.Setup(x => x.IsEmpty()).Returns(false);
        _queue.Setup(x => x.Take(It.IsAny<int>())).Returns(new[] { new DbOutboxMessage() });
        _delayWrapper.Setup(x => x.SafeDelay(It.IsAny<TimeSpan>(), It.IsAny<CancellationToken>())).Callback(() => { _cts.Cancel(); });

        await _sut.StartAsync(_cts.Token);

        _sender.Verify(x => x.SendOutboxMessages(It.IsAny<IEnumerable<DbOutboxMessage>>(), It.IsAny<CancellationToken>()));
    }

    [Fact]
    public async Task ExecuteAsync_ShouldHandleExceptionAndKeepProcessing_WhenSenderThrowsException()
    {
        _sender.Setup(x => x.SendOutboxMessages(It.IsAny<IEnumerable<DbOutboxMessage>>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("TESTS"));

        int callsCount = 0;
        _delayWrapper.Setup(x => x.SafeDelay(It.IsAny<TimeSpan>(), It.IsAny<CancellationToken>()))
            .Callback(() =>
            {
                if (callsCount > 0) _cts.Cancel();
                callsCount++;
            });

        _queue.SetupSequence(x => x.IsEmpty()).Returns(false).Returns(true);

        await _sut.StartAsync(_cts.Token);

        _delayWrapper.Verify(x => x.SafeDelay(It.IsAny<TimeSpan>(), It.IsAny<CancellationToken>()), Times.Exactly(2));
    }

    private static CancellationTokenSource CreateCancellationTokenSource(int cancelAfterMs = 1000)
    {
        var cts = new CancellationTokenSource();
        cts.CancelAfter(cancelAfterMs);
        return cts;
    }
}