using AMI.AmanoOne.Cloud.DynamoDb.Internal.Outbox;
using AMI.AmanoOne.Cloud.DynamoDb.Internal.Outbox.Background;

namespace DynamoDb.Tests.Unit.Outbox.Background;

public class OutboxMessageQueueTests
{
    private readonly OutboxMessageQueue _sut = new();

    [Fact]
    public void IsEmpty_ShouldBeFalse_WhenQueueHasItem()
    {
        _sut.IsEmpty().Should().BeTrue();

        _sut.Add(new DbOutboxMessage());

        _sut.IsEmpty().Should().BeFalse();
    }

    [Fact]
    public void Take_ShouldReturnItem_WhenQueueHasItem()
    {
        _sut.Add(new DbOutboxMessage());

        var items = _sut.Take();
        items.Should().HaveCount(1);
    }

    [Fact]
    public void Take_ShouldReturnEmptyArray_WhenQueueIsEmpty()
    {
        var items = _sut.Take();
        items.Should().HaveCount(0);
    }

    [Fact]
    public void Take_ShouldOnlyReturnSpecifiedSize_WhenQueueContainsManyItems()
    {
        _sut.Add(new DbOutboxMessage());
        _sut.Add(new DbOutboxMessage());
        _sut.Add(new DbOutboxMessage());
        _sut.Add(new DbOutboxMessage());
        _sut.Add(new DbOutboxMessage());

        var items = _sut.Take(2);
        items.Should().HaveCount(2);
    }

    [Fact]
    public void Take_ShouldOnlyReturnDefaultSizeOf10_WhenQueueContainsLotsOfItems()
    {
        for (int i = 0; i < 200; i++)
        {
            _sut.Add(new DbOutboxMessage());
        }

        var items = _sut.Take();
        items.Should().HaveCount(10);
    }

    [Fact]
    public void Take_ShouldRemoveItemFromQueue()
    {
        _sut.Add(new DbOutboxMessage());
        _sut.Add(new DbOutboxMessage());

        var items = _sut.Take(2);
        items.Should().HaveCount(2);

        _sut.IsEmpty().Should().BeTrue();
    }
}