// Global using directives

global using System.Net;
global using System.Text.Json;
global using System.Threading.Tasks;
global using Amazon.DynamoDBv2;
global using Amazon.DynamoDBv2.DataModel;
global using Amazon.DynamoDBv2.Model;
global using AMI.AmanoOne.Cloud.DynamoDb;
global using DynamoDb.Tests.Integration.Shared;
global using Xunit;
global using FluentAssertions;
global using Microsoft.Extensions.DependencyInjection;
global using Microsoft.Extensions.Logging;
global using Moq;