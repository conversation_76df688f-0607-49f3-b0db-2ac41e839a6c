namespace AMI.AmanoOne.Cloud.DynamoDb;

/// <summary>
/// This simplifies the Dynamo DB Context interface so that it can be more easily tested.
/// </summary>
public interface IDynamoDbWrapper
{
    Task<List<T>> GetAll<T>(CancellationToken cancellationToken = default);
    Task<T> GetByHash<T>(object hashKey, CancellationToken cancellationToken = default);
    Task<T> GetByHash<T>(object hashKey, bool consistentRead = false, CancellationToken cancellationToken = default);
    Task SaveAsync<T>(T value, CancellationToken cancellationToken = default);
    Task<IEnumerable<T>> GetBatch<T, C>(List<C> hashKeys);
    Task Delete<T>(object hashKey, CancellationToken cancellationToken = default);
}