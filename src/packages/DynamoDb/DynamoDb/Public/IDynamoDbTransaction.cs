namespace AMI.AmanoOne.Cloud.DynamoDb;

public interface IDynamoDbTransaction
{
    Task<List<T>> GetAll<T>(CancellationToken cancellationToken = default);
    Task<T> GetByHash<T>(object hashKey, CancellationToken cancellationToken = default);
    Task<IEnumerable<T>> GetBatch<T, TC>(List<TC> hashKeys);
    void Upsert<T>(T item);
    void Delete<T>(T item);
    void Delete<T>(object hashKey);
    void AddMessage(OutboxMessage message);
    Task SaveChangesAsync(CancellationToken cancellationToken = default);
}

public class OutboxMessage
{
    public Guid Id { get; init; } = Guid.NewGuid();

    public string Topic { get; init; } = string.Empty;

    public object? Payload { get; init; }
}