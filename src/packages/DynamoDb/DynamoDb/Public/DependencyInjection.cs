using System.Diagnostics.CodeAnalysis;
using Amazon.DynamoDBv2;
using Amazon.Extensions.NETCore.Setup;
using Amazon.Runtime;
using AMI.AmanoOne.Cloud.DynamoDb.Internal;
using AMI.AmanoOne.Cloud.DynamoDb.Internal.Outbox;
using AMI.AmanoOne.Cloud.DynamoDb.Internal.Outbox.Background;
using AMI.AmanoOne.Cloud.DynamoDb.Internal.Services;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;

namespace AMI.AmanoOne.Cloud.DynamoDb;

[ExcludeFromCodeCoverage]
public static class DependencyInjection
{
    public static IServiceCollection AddDynamoDb(this IServiceCollection services, IConfiguration config)
    {
        var awsOptions = config.GetAWSOptions();
        var useLocalConfig = config.GetValue("AWS:UseLocalConfig", false);
        var localServiceUrl = config.GetValue("DYNAMO_LOCAL_URL", string.Empty);

        services.AddDefaultAWSOptions(awsOptions);
        services.TryAddSingleton<IDynamoDbWrapper, DynamoDbWrapper>();
        services.TryAddTransient<IDateTime, SystemDateTime>();
        services.TryAddScoped<IDynamoDbTransaction, DynamoDbTransaction>();

        SetupDynamoDbContext(services, localServiceUrl, useLocalConfig, awsOptions);

        return services;
    }

    public static IServiceCollection AddDynamoDbOutbox(this IServiceCollection services, Action<DbOutboxOptions>? action = null)
    {
        var options = new DbOutboxOptions();
        action?.Invoke(options);
        services.TryAddSingleton(options);
        services.TryAddSingleton<IOutboxMessageQueue, OutboxMessageQueue>();
        services.TryAddTransient<IOutboxMessageSender, OutboxMessageSender>();
        services.TryAddTransient<IDelayWrapper, DelayWrapper>();
        services.AddHostedService<SenderBackgroundService>();

        return services;
    }

    private static void SetupDynamoDbContext(IServiceCollection services,
        string localServiceUrl,
        bool useLocalConfig,
        AWSOptions awsOptions)
    {
        if (!string.IsNullOrEmpty(localServiceUrl) || useLocalConfig)
        {
            var serviceUrl = useLocalConfig ? awsOptions.DefaultClientConfig.ServiceURL : localServiceUrl;
            // For local development and integration testing to point at LocalStack. Should not be used for production
            services.AddSingleton<IDynamoDBContext, DynamoDBContext>(opt =>
            {
                var amazonDynamoDB = new AmazonDynamoDBClient("123", "123",
                    new AmazonDynamoDBConfig
                    {
                        RegionEndpoint = Amazon.RegionEndpoint.USEast2,
                        UseHttp = true,
                        ServiceURL = serviceUrl
                    });
                return new DynamoDBContext(amazonDynamoDB);
            });
            services.AddAWSService<IAmazonDynamoDB>(options: new AWSOptions
            {
                Region = Amazon.RegionEndpoint.USEast2,
                Credentials = new BasicAWSCredentials("123", "123")
            });
        }
        else
        {
            services.AddAWSService<IAmazonDynamoDB>();
            services.TryAddSingleton<IDynamoDBContext, DynamoDBContext>();
        }
    }
}