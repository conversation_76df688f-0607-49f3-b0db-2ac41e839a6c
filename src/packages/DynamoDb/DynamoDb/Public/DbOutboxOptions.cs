using System.Diagnostics.CodeAnalysis;

namespace AMI.AmanoOne.Cloud.DynamoDb;

[ExcludeFromCodeCoverage]
public class DbOutboxOptions
{
    /// <summary>
    /// Amount of time before evaluating the queue again when there was nothing in the queue
    /// </summary>
    public TimeSpan EmptyQueueDelay { get; set; } = TimeSpan.FromMilliseconds(500);

    /// <summary>
    /// Amount of time before evaluating the queue again after processing some messages that were in the queue
    /// </summary>
    public TimeSpan SuccessfulProcessingDelay { get; set; } = TimeSpan.FromMilliseconds(100);

    /// <summary>
    /// Amount of time before evaluating the queue again after encountering an error when sending outbox messages
    /// </summary>
    public TimeSpan ErrorDelay { get; set; } = TimeSpan.FromSeconds(2);
}