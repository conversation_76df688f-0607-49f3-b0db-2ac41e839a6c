namespace AMI.AmanoOne.Cloud.DynamoDb.Internal;

/// <summary>
/// <inheritdoc cref="IDynamoDbWrapper"/>
/// </summary>
internal class DynamoDbWrapper : IDynamoDbWrapper
{
    private readonly IDynamoDBContext _dynamoDbContext;

    public DynamoDbWrapper(IDynamoDBContext dynamoDbContext)
    {
        _dynamoDbContext = dynamoDbContext;
    }

    public async Task Delete<T>(object hashKey, CancellationToken cancellationToken = default)
    {
        await _dynamoDbContext.DeleteAsync<T>(hashKey, cancellationToken);
    }

    public async Task<List<T>> GetAll<T>(CancellationToken cancellationToken = default)
    {
        var allDocs = await _dynamoDbContext.ScanAsync<T>(new List<ScanCondition>()).GetRemainingAsync(cancellationToken);
        return allDocs;
    }

    public async Task<IEnumerable<T>> GetBatch<T, C>(List<C> hashKeys)
    {
        BatchGet<T> batchRequest = _dynamoDbContext.CreateBatchGet<T>();
        foreach (C key in hashKeys)
        {
            batchRequest.AddKey(key);
        }

        await batchRequest.ExecuteAsync();
        return batchRequest.Results;
    }

    public async Task<T> GetByHash<T>(object hashKey, bool consistentRead = false, CancellationToken cancellationToken = default)
    {
        var config = new DynamoDBOperationConfig
        {
            ConsistentRead = consistentRead // use consistent read to get the latest version of the document
        };
        var document = await _dynamoDbContext.LoadAsync<T>(hashKey, config, cancellationToken);
        return document;
    }
    public async Task<T> GetByHash<T>(object hashKey, CancellationToken cancellationToken = default)
    {
        var document = await _dynamoDbContext.LoadAsync<T>(hashKey, cancellationToken);
        return document;
    }

    public async Task SaveAsync<T>(T value, CancellationToken cancellationToken = default)
    {
        await _dynamoDbContext.SaveAsync(value, cancellationToken);
    }
}