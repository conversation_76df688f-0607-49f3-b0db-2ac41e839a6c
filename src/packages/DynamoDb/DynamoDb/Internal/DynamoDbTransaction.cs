using System.Text.Json;
using AMI.AmanoOne.Cloud.DynamoDb.Internal.Outbox;
using AMI.AmanoOne.Cloud.DynamoDb.Internal.Outbox.Background;
using AMI.AmanoOne.Cloud.DynamoDb.Internal.Services;

namespace AMI.AmanoOne.Cloud.DynamoDb.Internal;

internal class DynamoDbTransaction : IDynamoDbTransaction
{
    private readonly IDynamoDBContext _context;
    private readonly IOutboxMessageQueue? _queue;
    private readonly IDateTime _dateTime;
    private readonly List<BatchWrite> _batchWrites = new();
    private readonly List<DbOutboxMessage> _messages = new();

    public DynamoDbTransaction(IDynamoDBContext context, IDateTime dateTime, IOutboxMessageQueue? queue = null)
    {
        _context = context;
        _queue = queue;
        _dateTime = dateTime;
    }

    public async Task<List<T>> GetAll<T>(CancellationToken cancellationToken = default)
    {
        return await _context.ScanAsync<T>(new List<ScanCondition>()).GetRemainingAsync(cancellationToken);
    }

    public async Task<IEnumerable<T>> GetBatch<T, TC>(List<TC> hashKeys)
    {
        var batchRequest = _context.CreateBatchGet<T>();
        foreach (var key in hashKeys)
        {
            batchRequest.AddKey(key);
        }

        await batchRequest.ExecuteAsync();
        return batchRequest.Results;
    }

    public async Task<T> GetByHash<T>(object hashKey, CancellationToken cancellationToken = default)
    {
        return await _context.LoadAsync<T>(hashKey, cancellationToken);
    }

    public void Upsert<T>(T item)
    {
        var batchWrite = Get<T>();
        batchWrite.AddPutItem(item);
    }

    public void Delete<T>(T item)
    {
        var batchWrite = Get<T>();
        batchWrite.AddDeleteItem(item);
    }

    public void Delete<T>(object hashKey)
    {
        var batchWrite = Get<T>();
        batchWrite.AddDeleteKey(hashKey);
    }

    public void AddMessage(OutboxMessage message)
    {
        var dbOutboxMessage = new DbOutboxMessage
        {
            Key = message.Id,
            Created = _dateTime.UtcNow,
            Payload = JsonSerializer.Serialize(message)
        };
        _messages.Add(dbOutboxMessage);
        var batchWrite = Get<DbOutboxMessage>();
        batchWrite.AddPutItem(dbOutboxMessage);
    }

    public async Task SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        var singleWrite = _context.CreateMultiTableBatchWrite(_batchWrites.ToArray());
        await singleWrite.ExecuteAsync(cancellationToken);
        _batchWrites.Clear();

        foreach (var message in _messages)
        {
            _queue?.Add(message);
        }

        _messages.Clear();
    }

    private BatchWrite<T> Get<T>()
    {
        if (_batchWrites.Any(x => x.GetType() == typeof(BatchWrite<T>)))
        {
            return (_batchWrites.First(x => x.GetType() == typeof(BatchWrite<T>)) as BatchWrite<T>)!;
        }

        var batchWrite = _context.CreateBatchWrite<T>();
        _batchWrites.Add(batchWrite);
        return batchWrite;
    }
}