using System.Diagnostics.CodeAnalysis;

namespace AMI.AmanoOne.Cloud.DynamoDb.Internal.Services;

internal interface IDelayWrapper
{
    Task SafeDelay(TimeSpan timeSpan, CancellationToken token);
}

[ExcludeFromCodeCoverage(Justification = "Simple Task delay wrapper")]
internal class DelayWrapper : IDelayWrapper
{
    public async Task SafeDelay(TimeSpan delay, CancellationToken token)
    {
        try
        {
            await Task.Delay(delay, token);
        }
        catch (TaskCanceledException)
        {
        }
    }
}