using System.Text.Json;
using AMI.AmanoOne.Cloud.EventQueuing;
using Microsoft.Extensions.Logging;

namespace AMI.AmanoOne.Cloud.DynamoDb.Internal.Outbox;

internal interface IOutboxMessageSender
{
    Task SendOutboxMessages(IEnumerable<DbOutboxMessage> messages, CancellationToken cancellationToken);
}

internal class OutboxMessageSender : IOutboxMessageSender
{
    private readonly IQueuePublisher _queuePublisher;
    private readonly IDynamoDBContext _context;
    private readonly ILogger<OutboxMessageSender> _logger;

    public OutboxMessageSender(IQueuePublisher queuePublisher, IDynamoDBContext context, ILogger<OutboxMessageSender> logger)
    {
        _queuePublisher = queuePublisher;
        _logger = logger;
        _context = context;
    }

    public async Task SendOutboxMessages(IEnumerable<DbOutboxMessage> messages, CancellationToken cancellationToken)
    {
        var sentMessages = new List<DbOutboxMessage>();
        foreach (var dbMessage in messages)
        {
            try
            {
                var parsedMessage = JsonSerializer.Deserialize<OutboxMessage>(dbMessage.Payload);
                var success = await _queuePublisher.PublishAsync(new PublishQueueMessage
                {
                    MessageId = parsedMessage!.Id,
                    Topic = parsedMessage.Topic,
                    JsonPayload = JsonSerializer.Serialize(parsedMessage.Payload)
                }, cancellationToken);
                
                if (!success)
                {
                    _logger.LogWarning("Failed to publish message to Queue from DynamoDb outbox. Message ID: {MessageId}", dbMessage.Key);
                    continue;
                }

                sentMessages.Add(dbMessage);
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Failed to publish message to Queue. Message ID: {MessageId}", dbMessage.Key);
            }
        }

        if (!sentMessages.Any())
        {
            return;
        }

        var batchWrite = _context.CreateBatchWrite<DbOutboxMessage>();
        foreach (var message in (IEnumerable<DbOutboxMessage>)sentMessages)
        {
            batchWrite.AddDeleteKey(message.Key);
        }

        await batchWrite.ExecuteAsync(cancellationToken);
    }
}