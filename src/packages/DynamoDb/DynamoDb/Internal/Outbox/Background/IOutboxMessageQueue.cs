using System.Collections.Concurrent;

namespace AMI.AmanoOne.Cloud.DynamoDb.Internal.Outbox.Background;

internal interface IOutboxMessageQueue
{
    void Add(DbOutboxMessage message);
    bool IsEmpty();
    DbOutboxMessage[] Take(int size = 10);
}

internal class OutboxMessageQueue : IOutboxMessageQueue
{
    private readonly ConcurrentQueue<DbOutboxMessage> _queue = new();

    public void Add(DbOutboxMessage message)
    {
        _queue.Enqueue(message);
    }

    public bool IsEmpty()
    {
        return _queue.IsEmpty;
    }

    public DbOutboxMessage[] Take(int size = 10)
    {
        if (_queue.IsEmpty)
        {
            return Array.Empty<DbOutboxMessage>();
        }

        var list = new List<DbOutboxMessage>();
        while (_queue.TryDequeue(out var value))
        {
            list.Add(value);
            if (list.Count >= size)
            {
                break;
            }
        }

        return list.ToArray();
    }
}