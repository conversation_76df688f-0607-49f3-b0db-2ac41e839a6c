using AMI.AmanoOne.Cloud.DynamoDb.Internal.Services;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace AMI.AmanoOne.Cloud.DynamoDb.Internal.Outbox.Background;

internal class SenderBackgroundService : BackgroundService
{
    private readonly IOutboxMessageQueue _messageQueue;
    private readonly ILogger<SenderBackgroundService> _logger;
    private readonly IOutboxMessageSender _sender;
    private readonly IDelayWrapper _delayWrapper;
    private readonly DbOutboxOptions _outboxOptions;

    public SenderBackgroundService(IOutboxMessageQueue messageQueue,
        IOutboxMessageSender sender,
        IDelayWrapper delayWrapper,
        DbOutboxOptions outboxOptions,
        ILogger<SenderBackgroundService> logger)
    {
        _messageQueue = messageQueue;
        _logger = logger;
        _sender = sender;
        _delayWrapper = delayWrapper;
        _outboxOptions = outboxOptions;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                if (_messageQueue.IsEmpty())
                {
                    await _delayWrapper.SafeDelay(_outboxOptions.EmptyQueueDelay, stoppingToken);
                    continue;
                }

                var messages = _messageQueue.Take();
                await _sender.SendOutboxMessages(messages, stoppingToken);
                await _delayWrapper.SafeDelay(_outboxOptions.SuccessfulProcessingDelay, stoppingToken);
            }
            catch (Exception exception)
            {
                _logger.LogError(exception, "Error during Outbox Queue processing");
                await _delayWrapper.SafeDelay(_outboxOptions.ErrorDelay, stoppingToken);
            }
        }
    }
}