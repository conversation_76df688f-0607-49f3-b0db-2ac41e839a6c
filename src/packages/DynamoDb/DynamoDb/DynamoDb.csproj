<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
    <PackageId>AMI.AmanoOne.Cloud.DynamoDb</PackageId>
    <Version>1.0.0</Version>
    <AssemblyName>AMI.AmanoOne.Cloud.DynamoDb</AssemblyName>
    <RootNamespace>AMI.AmanoOne.Cloud.DynamoDb</RootNamespace>
    <Authors>AmanoMcgann</Authors>
    <Company>AmanoMcgann</Company>
  </PropertyGroup>
  
  <ItemGroup>
    <PackageReference Include="AWSSDK.DynamoDBv2" Version="*********" />
    <PackageReference Include="AWSSDK.Extensions.NETCore.Setup" Version="*******" />
    <PackageReference Include="Microsoft.Extensions.Diagnostics.HealthChecks" Version="6.0.14" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="6.0.1" />
    <PackageReference Include="Microsoft.Extensions.Hosting.Abstractions" Version="6.0.0" />
    <PackageReference Include="Microsoft.Extensions.Options" Version="6.0.0" />
    <PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="6.0.0" />
    <PackageReference Include="AMI.AmanoOne.Cloud.EventQueuing.Abstractions" Version="1.0.13" />
  </ItemGroup>

  <ItemGroup>
    <InternalsVisibleTo Include="DynamoDb.Tests" />
    <InternalsVisibleTo Include="DynamicProxyGenAssembly2" />
  </ItemGroup>


  <ItemGroup>
    <None Remove="DynamoDb.csproj.DotSettings" />
  </ItemGroup>
  
</Project>
