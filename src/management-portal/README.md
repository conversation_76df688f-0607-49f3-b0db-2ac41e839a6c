# Device Portal  

This is the codebase for the static web site for the portal. Includes pipeline script to deploy to S3 after merge, to serve as a static web site.

# Prerequisites

`git`, `nodejs`, `npm`

### Node.js

Download and install Node.js
https://nodejs.org/en/

# Launching

### Install Node.js dependencies

In the main directory, run `npm i`

### Setup Environment

1. Add a .env file to the root of the project
2. Add the necessary environmant values to that .env file based on the .sample.env file

### Launch Static Site

In the main directory, run `npm start`

This command will pack, launch a server to serve static files locally, and launch a web browser to view the static site.

To have react app interact with local environment run the command `npm run start:local`

# Unit Test

In the main directory, run `npm test` to start the interactive unit testing experience

# API Request Cancellation

When making long running api requests, we also need to ability to cancel them when unmounting the component they were initiated from, or cancel subsequent requests in the case of search terms or filters changing.

In order to accomplish this, use the `useCancellationToken` hook which will manage the lifetime of your api request, and automatically cancel, subsequent requests.

## Usage

```js
const [loading, setLoading] = useState(false);
const { execute, inProgress } = useCancellationToken({
  func: myApiRequest,
  errHandleFunc: myErrorHandler, // what you want to do when a non-cancellation exception is thrown from the api request
});

useEffect(() => {
  execute({ someParameter1, someParameter2 });
}, [someParameter1, someParameter2]);

useEffect(() => {
  setLoading(inProgress);
}, [inProgress]);

/**
 the parameters of your function must be a destructured object so that the useCancellationToken hook can provide you with a cancelToken, and what other parameters you passed into your request
 */

async function myApiRequest({ someParameter1, someParameter2, cancelToken }) {
  // DO NOT TRY CATCH; that is handled by the useCancellationToken hook
  await someService.getTheThing({
    someParameter1,
    someParameter2,
    cancelToken,
  });
}

async function myErrorHandler(err) {
  // do something with the err
}
```

### Additional Comments

You, as the developer, do not need to worry about manually cancelling the request on a useEffect unmount. The `useCancellationToken` hook will handle cancelling the request for you. However, you do have the ability to manually cancel the request if you need to for whatever reason.

Example of cancelling the request manually:

```js
const { execute, cancel } = useCancellationToken({
  func: myApiRequest,
});

useEffect(() => {
  execute({ someParameter1, someParameter2 });
}, [someParameter1, someParameter2]);

// just a trivial example
useEffect(() => {
  if (someCancellationIdentifier) cancel();
}, [someCancellationIdentifier]);
```

## Testing

If you are writing a test suite that is using the `renderWithRedux` or `renderWithReduxAndTheme` utilities, you will need to delete the instance of that store after each test to make sure the test does not hold on to stale stores that could cause tests to become brittle.

```javascript
afterEach(() => {
  // import from the testUtils index file!
  cleanup();
});
```

### Timer Leaks
```javascript
// ❌ Problem: Timers not cleaned up
test('animation test', () => {
  setTimeout(() => {
    // ...
  }, 1000);
});

// ✅ Fix: Clean up timers
test('animation test', () => {
  const timer = setTimeout(() => {
    // ...
  }, 1000);
  
  // In cleanup
  clearTimeout(timer);
});

// Or use fake timers
beforeEach(() => {
  jest.useFakeTimers();
});

afterEach(() => {
  jest.runOnlyPendingTimers();
  jest.useRealTimers();
});
```

### DOM Not Clean
```javascript
// ❌ Problem: Elements left in DOM
test('render test', () => {
  document.body.appendChild(someElement);
});

// ✅ Fix: The enhanced cleanup should handle this
// If not, manually clean:
afterEach(() => {
  document.body.innerHTML = '';
});
```

### Redux Conflicts
```javascript
// Debug output shows redux-dispatch conflicts?
// This means tests are dispatching actions that affect each other

// ✅ Fix 1: Use unique test data
test('create user', () => {
  const testId = Date.now() + Math.random();
  dispatch(createUser({ id: testId, email: `test${testId}@example.com` }));
});

// ✅ Fix 2: Reset specific slices
afterEach(() => {
  store.dispatch({ type: 'user/reset' });
  store.dispatch({ type: 'entities/clear' });
});
```

## 6. Quick Debugging Checklist

When you see tests passing with `--runInBand` but failing in pipeline:

1. **Look for leak warnings** in the output
2. **remove the `--silent` ** from the package.json test script
3. **Check for resource conflicts** at the end of test run
4. **Focus on the specific warnings**:
    - Store leaks → Check afterEach cleanup
    - Timer leaks → Add timer cleanup or use fake timers
    - DOM issues → Verify cleanup() is called
    - Redux conflicts → Use unique data or reset state

## 7. Using Debug Utilities in Specific Tests

```javascript
import { debugUtils } from './test-utils';

test('debugging a specific issue', () => {
  // Check store count before
  console.log('Stores before:', debugUtils.getStoreCount());
  
  // Your test code...
  
  // Check resource access
  const accesses = debugUtils.getResourceAccess();
  console.log('Redux actions:', accesses.filter(a => a.type === 'redux-dispatch'));
});
```

## 8. Emergency: Still Can't Find the Issue?

Add this to the problem test file:

```javascript
// At the top of the problematic test file
beforeAll(() => {
  console.log('=== TEST FILE START ===', expect.getState().testPath);
  console.log('Initial state:', {
    stores: document.querySelectorAll('[data-test-store]').length,
    timers: setTimeout.toString(),
    dom: document.body.children.length
  });
});

afterAll(() => {
  console.log('=== TEST FILE END ===');
});

// Around the problematic test
test('problematic test', () => {
  console.log('Before action:', store.getState());
  // Your test action
  console.log('After action:', store.getState());
});
```

## Most Common Culprits

Based on your test utilities, the most likely issues are:

1. **Multiple stores being created** - The debug mode will show store leak warnings
2. **Shared Redux state** - Look for redux-dispatch conflicts in debug output
3. **Cleanup not being called** - Watch for "DOM not clean" warnings
4. **Tests modifying the same entities** - Check if tests use hardcoded IDs


## 1. Analyze Debug Output

Look for these warning signs in the console:

```
⚠️ 2 store(s) leaked from previous test
⚠️ DOM not clean: 3 element(s) in body
⚠️ 1 timer(s) leaked from previous tests
🚨 Potential resource conflicts detected:
   - redux-dispatch conflict between "test A" and "test B" (45ms apart)
```

## 2. Common Fixes Based on Debug Output

### Store Leaks
```javascript
// ❌ Problem: Multiple stores created
const store = renderWithRedux(...);

// ✅ Fix: Ensure cleanup
afterEach(() => {
  cleanup(); // This should handle it with our enhanced utilities
});
```

### Timer Leaks
```javascript
// ❌ Problem: Timers# Debugging Parallel Test Issues - Quick Guide

## 1. Enable Debug Mode

```bash
# Run tests with debugging enabled
DEBUG_TESTS=true npm test

# Or for specific test files
DEBUG_TESTS=true jest user.test.js

# Find which tests conflict
DEBUG_TESTS=true jest --runInBand  # Should pass
DEBUG_TESTS=true jest              