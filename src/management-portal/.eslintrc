{"env": {"browser": true, "es6": true, "jest": true}, "extends": ["eslint:recommended", "react-app", "plugin:react/recommended"], "globals": {"Atomics": "readonly", "SharedArrayBuffer": "readonly"}, "parserOptions": {"ecmaFeatures": {"jsx": true}, "ecmaVersion": 2018, "sourceType": "module"}, "plugins": ["react", "jest"], "rules": {"indent": "off", "quotes": "off", "semi": "off", "eqeqeq": "off", "default-case": "off", "no-console": "warn", "no-prototype-builtins": "off", "no-debugger": "warn", "brace-style": ["error", "1tbs", {"allowSingleLine": true}], "react/jsx-uses-react": 1, "react/prop-types": 0, "jest/expect-expect": "error", "jest/valid-expect": "error", "jest/no-focused-tests": "error", "jest/no-identical-title": "error", "jest/no-truthy-falsy": "error", "jest/no-test-prefixes": "error", "jest/prefer-hooks-on-top": "warn", "jest/no-commented-out-tests": "off", "no-mixed-spaces-and-tabs": "off"}, "settings": {"react": {"version": "detect"}}}