parameters:
  - name: Environment
    type: string
  - name: ArtifactName
    type: string
  - name: PoolID
    type: string
  - name: CognitoClientID
    type: string
  - name: MobilePayAuthProvider
    type: string
  - name: MobilePayClientID
    type: string
  - name: MobilePayClientSecret
    type: string
  - name: StunUrl
    type: string
  - name: TurnUrl
    type: string
  - name: TurnUsername
    type: string
  - name: TurnPassword
    type: string

steps:
  - bash: npm cache verify
  - task: Cache@2
    inputs:
      key: 'npm | "$(Agent.OS)" | package-lock.json'
      path: $(npm_config_cache)
      restoreKeys: |
        npm | "$(Agent.OS)"
        npm
  - bash: "npm ci"
    displayName: Install dependencies
  - script: |
      echo COGNITO_USER_POOL_ID = \"${{parameters.PoolID}}\" >> .env
      echo COGNITO_CLIENT_ID = \"${{parameters.CognitoClientID}}\" >> .env
      echo COGNITO_MOBILE_PAY_AUTH_PROVIDER = \"${{parameters.MobilePayAuthProvider}}\" >> .env
      echo COGNITO_MOBILE_PAY_CLIENT_ID = \"${{parameters.MobilePayClientID}}\" >> .env
      echo COGNITO_MOBILE_PAY_CLIENT_SECRET = \"${{parameters.MobilePayClientSecret}}\" >> .env
      echo STUN_URL = \"${{parameters.StunUrl}}\" >> .env
      echo TURN_URL = \"${{parameters.TurnUrl}}\" >> .env
      echo TURN_USERNAME = \"${{parameters.TurnUsername}}\" >> .env
      echo TURN_PASSWORD = \"${{parameters.TurnPassword}}\" >> .env
      cat .env
    displayName: Add ENV Vars
  - bash: |
      export NODE_OPTIONS=--max_old_space_size=16384
      npm run build:${{parameters.Environment}}
    displayName: Build ${{parameters.Environment}} bundle
  - task: PublishPipelineArtifact@1
    inputs:
      TargetPath: "./build"
      ArtifactName: "${{parameters.ArtifactName}}"
