parameters:
  - name: <PERSON><PERSON>ey
    type: string
  - name: AccessSecret
    type: string
  - name: Role
    type: string
  - name: ArtifactName
    type: string
  - name: BucketName
    type: string
  - name: StagingDirectory
    type: string

steps:
  - task: DownloadPipelineArtifact@2
    displayName: Download Artifact
    inputs:
      buildType: "current"
      artifactName: ${{parameters.ArtifactName}}
      itemPattern: "**"
      path: ${{parameters.StagingDirectory}}
  - bash: |
      creds=$(aws sts assume-role --role-arn ${{parameters.Role}} \
      --role-session-name az-devops-device-portal \
      --output text \
      --query='Credentials.[join(`=`, [`AWS_ACCESS_KEY_ID`, AccessKeyId]), join(`=`, [`AWS_SECRET_ACCESS_KEY`, SecretAccessKey]), join(`=`, [`AWS_SESSION_TOKEN`, SessionToken])]')
      eval "export $creds"
      aws s3 cp '$(Pipeline.Workspace)/${{parameters.ArtifactName}}' s3://${{parameters.BucketName}}/ --recursive --acl public-read
    env:
      AWS_ACCESS_KEY_ID: ${{parameters.AccessKey}}
      AWS_SECRET_ACCESS_KEY: ${{parameters.AccessSecret}}
    displayName: Upload artifact to S3
