// retryTest.js
export const retryTest = (testFn, retries = 3) => async () => {
    let lastError;
    for (let attempt = 1; attempt <= retries; attempt++) {
      try {
        await testFn();
        return; // Test passed, exit the retry loop
      } catch (error) {
        lastError = error;
        console.warn(`Retrying test, attempt ${attempt} failed.`);
      }
    }
    throw lastError; // All retries failed, throw the last error
  };
  