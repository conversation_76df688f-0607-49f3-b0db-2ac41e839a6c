import React from "react";
import { ThemeProvider, createMuiTheme } from "@material-ui/core/styles";
import { act } from "react-dom/test-utils";
import { applyMiddleware, combineReducers, createStore } from "redux";
import { Provider } from "react-redux";
import cashierReducer from "../src/state/slices/cashier/cashier";
import poeReducer from "../src/state/slices/POE/payOnEntry";
import userReducer from "../src/state/slices/user";
import entityReducer from "../src/state/slices/entities";
import thunk from "redux-thunk";
import { render, cleanup as rtlCleanup } from "@testing-library/react";
import callCenterReducer from "../src/state/slices/callcenter";
import shiftSessionReducer from "../src/state/slices/shiftSession/shiftSession.js";
import peripheralsReducer from "../src/state/slices/peripherals.js";
import countryReducer from "../src/state/slices/countries.js";
import entityScopeReducer from "../src/state/slices/entityScope.js";
import activityReducer from "../src/state/slices/filters/activity.js";
import coreEntityReducer from "../src/state/slices/CoreEntity/CoreEntity.js";
import { renderHook } from "@testing-library/react-hooks";
import { useForm, FormProvider } from "react-hook-form";


// Store tracking for debugging
const storeInstances = new Set();
const testMetadata = new Map();
const resourceAccess = [];

// Debug utilities
const debugLog = (...args) => {
    const testName = expect.getState()?.currentTestName || 'unknown';
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] [${testName}]`, ...args);
};

const trackResourceAccess = (type, details) => {
    resourceAccess.push({
        type,
        timestamp: Date.now(),
        test: expect.getState()?.currentTestName,
        suite: expect.getState()?.testPath,
        ...details
    });
};

// Helper to safely check timer count
const getTimerCount = () => {
    try {
        // Check if fake timers are in use
        if (typeof jest !== 'undefined' && jest.isMockFunction(setTimeout)) {
            return jest.getTimerCount();
        }
    } catch (e) {
        // Fake timers not in use
    }
    return 0;
};

// Enhanced cleanup that tracks issues
const enhancedCleanup = () => {
    const beforeCleanup = {
        stores: storeInstances.size,
        timers: getTimerCount(),
        documentBody: document.body.innerHTML.length
    };

    cleanup();

    const afterCleanup = {
        stores: storeInstances.size,
        timers: getTimerCount(),
        documentBody: document.body.innerHTML.length
    };

    if (afterCleanup.stores > 0) {
        console.warn('⚠️ Store instances not cleaned up:', afterCleanup.stores);
    }

    if (afterCleanup.documentBody > 0) {
        console.warn('⚠️ DOM not fully cleaned:', document.body.innerHTML.substring(0, 100) + '...');
    }

    if (beforeCleanup.timers > 0 && afterCleanup.timers > 0) {
        console.warn('⚠️ Timers possibly not cleaned up:', afterCleanup.timers);
    }
};

// Track store creation and modifications
const createTrackedStore = (reducers, initialState, enhancer) => {
    const store = createStore(reducers, initialState, enhancer);

    storeInstances.add(store);

    // Track all dispatches
    const originalDispatch = store.dispatch;
    store.dispatch = (action) => {
        trackResourceAccess('redux-dispatch', {
            actionType: action.type,
            hasPayload: !!action.payload
        });
        return originalDispatch(action);
    };

    // Track store in metadata
    testMetadata.set(expect.getState()?.currentTestName, {
        storeCreated: Date.now(),
        initialState: Object.keys(initialState || {})
    });

    return store;
};

export const actImmediate = (wrapper) =>
    act(
        () =>
            new Promise((resolve) => {
                setImmediate(() => {
                    wrapper.update();
                    resolve();
                });
            })
    );

const theme = createMuiTheme({
    palette: {
        amano: {
            warnings: {
                warning: "#123456",
            },
        },
    },
});

export const waitForComponentRender = async (wrapper) => {
    await act(async () => {
        await new Promise((resolve) => setTimeout(resolve, 0));
        wrapper.update();
    });
};

// Create combined reducers once to avoid recreation
const rootReducer = combineReducers({
    payOnEntry: poeReducer,
    entities: entityReducer,
    user: userReducer,
    cashier: cashierReducer,
    callcenter: callCenterReducer,
    shiftSession: shiftSessionReducer,
    peripherals: peripheralsReducer,
    countries: countryReducer,
    entityScope: entityScopeReducer,
    activity: activityReducer,
    coreEntities: coreEntityReducer,
});

const activeStores = new Map();

// Enhanced cleanup that also cleans stores
export const cleanup = () => {
    rtlCleanup();
    activeStores.forEach((store, id) => {
        storeInstances.delete(store);
    });
    activeStores.clear();
};

// Remove the afterEach from inside renderWithRedux
export const renderWithRedux = (ui, mockState) => {
    debugLog('renderWithRedux called', {
        hasState: !!mockState,
        stateKeys: mockState ? Object.keys(mockState) : []
    });

    const store = createTrackedStore(
        rootReducer,
        mockState || {},
        applyMiddleware(thunk)
    );

    // Track store creation
    storeInstances.add(store);

    const storeId = Math.random().toString(36);
    activeStores.set(storeId, store);
    const result = render(<Provider store={store}>{ui}</Provider>);
    result._storeId = storeId;

    // Add store reference and cleanup method to result
    result.store = store;
    result.cleanupStore = () => {
        storeInstances.delete(store);
    };

    // Add debug info to result
    result._debugInfo = {
        storeId: Math.random().toString(36).substr(2, 9),
        renderTime: Date.now()
    };

    return result;
};

export const renderWithReduxAndTheme = (ui, mockState) => {
    debugLog('renderWithReduxAndTheme called', {
        hasState: !!mockState,
        stateKeys: mockState ? Object.keys(mockState) : []
    });

    const store = createTrackedStore(
        rootReducer,
        mockState || {},
        applyMiddleware(thunk)
    );

    storeInstances.add(store);
    const storeId = Math.random().toString(36);
    activeStores.set(storeId, store);

    const result = render(
        <Provider store={store}>
            <ThemeProvider theme={theme}>
                {ui}
            </ThemeProvider>
        </Provider>
    );
    result._storeId = storeId;
    result.store = store;
    result.cleanupStore = () => {
        storeInstances.delete(store);
    };

    return result;
};

function getWrapper(store) {
    return ({ children }) => <Provider store={store}>{children}</Provider>;
}

export const renderHookWithRedux = (hook, mockState) => {
    debugLog('renderHookWithRedux called', {
        hasState: !!mockState,
        stateKeys: mockState ? Object.keys(mockState) : []
    });

    const store = createTrackedStore(
        rootReducer,
        mockState || {},
        applyMiddleware(thunk)
    );
    const storeId = Math.random().toString(36);
    activeStores.set(storeId, store);
    const wrapper = getWrapper(store);

    // Enhanced cleanup
    afterEach(() => {
        debugLog('Cleaning up after hook test');
        enhancedCleanup();

        // Remove store from tracking
        storeInstances.delete(store);
    });

    return renderHook(hook, { wrapper });
};

export const withMarkup = (query) => (text) =>
    query((content, node) => {
        const hasText = (node) => node.textContent === text;
        const childrenDontHaveText = Array.from(node.children).every(
            (child) => !hasText(child)
        );
        return hasText(node) && childrenDontHaveText;
    });

export const FormProviderWrapper = ({ children, defaultValues = {} }) => {
    debugLog('FormProviderWrapper rendered', { defaultValues });

    const methods = useForm({ defaultValues });
    const content = typeof children === "function" ? children(methods) : children;
    return <FormProvider {...methods}>{content}</FormProvider>;
};

// Global debug helpers
// Log test lifecycle
beforeEach(() => {
    const testName = expect.getState()?.currentTestName;
    debugLog('Starting test');

    // Check for leaked resources from previous test
    if (storeInstances.size > 0) {
        console.warn(`⚠️ ${storeInstances.size} store(s) leaked from previous test`);
    }
});

afterEach(() => {
    debugLog('Finishing test');
});

// Global teardown
afterAll(() => {
    if (resourceAccess.length > 0) {
        // Analyze resource access patterns
        const conflicts = [];

        resourceAccess.forEach((access, i) => {
            resourceAccess.forEach((other, j) => {
                if (i < j && access.test !== other.test && access.type === other.type) {
                    // Check if actions happened close together (within 100ms)
                    if (Math.abs(access.timestamp - other.timestamp) < 100) {
                        conflicts.push({
                            type: access.type,
                            test1: access.test,
                            test2: other.test,
                            timeDiff: Math.abs(access.timestamp - other.timestamp)
                        });
                    }
                }
            });
        });

        if (conflicts.length > 0) {
            console.error('🚨 Potential resource conflicts detected:');
            conflicts.forEach(conflict => {
                console.error(`   - ${conflict.type} conflict between "${conflict.test1}" and "${conflict.test2}" (${conflict.timeDiff}ms apart)`);
            });
        }

        console.log(`📊 Total resource accesses: ${resourceAccess.length}`);
        console.log(`📊 Unique tests: ${new Set(resourceAccess.map(a => a.test)).size}`);
    }
});

// Export debug utilities for use in specific tests
export const debugUtils = {
    enableDebug: () => { process.env.DEBUG_TESTS = 'true'; },
    getStoreCount: () => storeInstances.size,
    getResourceAccess: () => [...resourceAccess],
    getTestMetadata: () => new Map(testMetadata),
    clearDebugData: () => {
        storeInstances.clear();
        testMetadata.clear();
        resourceAccess.length = 0;
    }
};