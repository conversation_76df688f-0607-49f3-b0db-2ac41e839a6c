parameters:
  - name: Artif<PERSON><PERSON>ame
    type: string
  - name: Region
    type: string
  - name: Bucket<PERSON>ame
    type: string
  - name: StagingDirectory
    type: string
  - name: AccessKey
    type: string
  - name: AccessSecret
    type: string

steps:
  - task: DownloadPipelineArtifact@2
    displayName: Download Artifact
    inputs:
      buildType: "current"
      artifactName: ${{parameters.ArtifactName}}
      itemPattern: "**"
      path: ${{parameters.StagingDirectory}}
  - task: S3Upload@1
    inputs:
      regionName: ${{parameters.Region}}
      bucketName: ${{parameters.BucketName}}
      sourceFolder: ${{parameters.StagingDirectory}}
      globExpressions: "**"
      filesAcl: "public-read"
      logRequest: true
      logResponse: true
    env:
      AWS_ACCESS_KEY_ID: ${{parameters.AccessKey}}
      AWS_SECRET_ACCESS_KEY: ${{parameters.AccessSecret}}
