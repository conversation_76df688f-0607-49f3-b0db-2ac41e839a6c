{"name": "device-portal", "version": "0.1.0", "private": true, "scripts": {"start": "cross-env NODE_OPTIONS=--openssl-legacy-provider NODE_ENV=development BABEL_ENV=development node scripts/start.js", "start:local": "cross-env NODE_OPTIONS=--openssl-legacy-provider NODE_ENV=local BABEL_ENV=development node scripts/start.js", "build:local": "cross-env NODE_OPTIONS=--openssl-legacy-provider NODE_ENV=local BABEL_ENV=development node scripts/build.js", "build:dev": "cross-env NODE_OPTIONS=--openssl-legacy-provider --max-old-space-size=4096 NODE_ENV=development node scripts/build.js", "build:stage": "cross-env NODE_OPTIONS=--openssl-legacy-provider --max-old-space-size=4096 NODE_ENV=stage node scripts/build.js", "build:qa": "cross-env NODE_OPTIONS=--openssl-legacy-provider --max-old-space-size=4096 NODE_ENV=qa node scripts/build.js", "build:prod": "cross-env NODE_OPTIONS=--openssl-legacy-provider --max-old-space-size=4096 NODE_ENV=production GENERATE_SOURCEMAP=false node scripts/build.js", "test": "node scripts/test.js", "test:ci": "node scripts/test.js --watchAll=false --collect-coverage --maxWorkers=2 --clearMocks --silent", "lint": "eslint src/**/*.{js,jsx,ts,tsx}", "lint:fix": "npm run lint -- --fix", "eject": "react-scripts eject"}, "devDependencies": {"@babel/plugin-transform-runtime": "^7.25.9", "@pmmmwh/react-refresh-webpack-plugin": "^0.4.3", "@testing-library/jest-dom": "^5.1.1", "@testing-library/react": "^11.2.5", "@testing-library/react-hooks": "^5.1.1", "@testing-library/user-event": "^13.5.0", "babel-core": "^7.0.0-bridge.0", "babel-plugin-lodash": "^3.3.4", "babel-plugin-transform-react-remove-prop-types": "^0.4.24", "babel-preset-env": "^1.7.0", "cross-env": "^7.0.3", "enzyme": "^3.11.0", "enzyme-adapter-react-16": "^1.15.6", "eslint-plugin-jest": "^23.6.0", "image-webpack-loader": "^8.1.0", "jest": "26.6.3", "jest-environment-jsdom-fourteen": "0.1.0", "jest-launchdarkly-mock": "^0.2.1", "jest-resolve": "24.9.0", "jest-runner-eslint": "^1.0.1", "jest-watch-typeahead": "0.4.2", "jest-when": "^3.5.1", "lodash-webpack-plugin": "^0.11.6", "prettier-eslint": "^9.0.1", "react-beautiful-dnd-test-utils": "^4.1.1", "react-error-overlay": "6.0.9", "react-refresh": "^0.9.0", "redux-thunk": "^2.4.1"}, "dependencies": {"@babel/core": "7.7.4", "@date-io/moment": "^1.3.13", "@devexpress/analytics-core": "22.2.6", "@devexpress/dx-core": "^2.7.4", "@devexpress/dx-react-core": "^2.7.4", "@devexpress/dx-react-scheduler": "^2.7.4", "@devexpress/dx-react-scheduler-material-ui": "^2.7.4", "@fortawesome/fontawesome-svg-core": "^6.3.0", "@fortawesome/free-regular-svg-icons": "^6.3.0", "@fortawesome/free-solid-svg-icons": "^6.3.0", "@fortawesome/pro-duotone-svg-icons": "^6.3.0", "@fortawesome/pro-light-svg-icons": "^6.3.0", "@fortawesome/pro-regular-svg-icons": "^6.3.0", "@fortawesome/pro-solid-svg-icons": "^6.3.0", "@fortawesome/react-fontawesome": "^0.2.0", "@fullcalendar/interaction": "^6.1.4", "@fullcalendar/react": "^6.1.4", "@fullcalendar/timegrid": "^6.1.4", "@hookform/resolvers": "^2.8.3", "@material-ui/core": "^4.11.3", "@material-ui/data-grid": "^4.0.0-alpha.18", "@material-ui/icons": "^4.11.2", "@material-ui/lab": "^4.0.0-alpha.57", "@material-ui/pickers": "^3.2.10", "@microsoft/signalr": "^5.0.12", "@react-pdf/renderer": "^2.0.12", "@reduxjs/toolkit": "^1.6.0", "@svgr/webpack": "4.3.3", "@typescript-eslint/eslint-plugin": "^2.34.0", "@typescript-eslint/parser": "^2.34.0", "@zxing/browser": "0.0.9", "@zxing/library": "^0.18.4", "amazon-cognito-identity-js": "^3.3.3", "animate.css": "^4.1.1", "axios": "^0.19.2", "babel-eslint": "10.0.3", "babel-jest": "^24.9.0", "babel-loader": "8.0.6", "babel-plugin-named-asset-import": "^0.3.7", "babel-plugin-root-import": "^6.6.0", "babel-preset-react-app": "^9.1.0", "camelcase": "^5.3.1", "case-sensitive-paths-webpack-plugin": "2.2.0", "chart.js": "^2.9.4", "clsx": "^1.1.1", "core-js": "^3.8.3", "css-loader": "3.2.0", "devexpress-reporting": "22.2.6", "devextreme": "22.2.6", "dotenv": "8.2.0", "dotenv-expand": "5.1.0", "draft-convert": "^2.1.10", "draft-js": "^0.11.7", "draft-js-export-html": "^1.4.1", "draftjs-to-html": "^0.9.1", "eslint": "^6.6.0", "eslint-config-react-app": "^5.1.0", "eslint-loader": "3.0.2", "eslint-plugin-flowtype": "3.13.0", "eslint-plugin-import": "2.18.2", "eslint-plugin-jsx-a11y": "6.2.3", "eslint-plugin-react": "7.16.0", "eslint-plugin-react-hooks": "^1.6.1", "fetch": "^1.1.0", "file-loader": "4.3.0", "formik": "^2.2.6", "formik-material-ui": "^2.0.1", "formik-material-ui-pickers": "0.0.8", "fs-extra": "^8.1.0", "html-webpack-plugin": "4.0.0-beta.5", "ical-js-parser": "^0.7.4", "launchdarkly-react-client-sdk": "^3.8.1", "lodash": "^4.17.20", "material-ui-color": "^1.2.0", "material-ui-cron": "^0.0.4", "material-ui-dropzone": "^3.5.0", "material-ui-image": "^3.3.2", "material-ui-phone-number": "^2.2.6", "mini-css-extract-plugin": "0.8.0", "moment": "^2.29.1", "moment-timezone": "^0.5.32", "mui-rte": "^1.29.0", "normalizr": "^3.6.1", "notistack": "^0.9.17", "optimize-css-assets-webpack-plugin": "5.0.3", "pnp-webpack-plugin": "1.5.0", "polly-js": "^1.8.0", "postcss-flexbugs-fixes": "4.1.0", "postcss-loader": "3.0.0", "postcss-normalize": "8.0.1", "postcss-preset-env": "6.7.0", "postcss-safe-parser": "4.0.1", "qrcode": "^1.4.4", "react": "^16.14.0", "react-app-polyfill": "^1.0.5", "react-app-rewired": "^2.1.8", "react-beautiful-dnd": "^13.1.0", "react-chartjs-2": "^2.11.1", "react-dev-utils": "10.0.0", "react-dom": "^16.14.0", "react-error-boundary": "^3.1.4", "react-hook-form": "^7.20.4", "react-howler": "^5.0.0", "react-idle-timer": "^5.7.2", "react-input-mask": "^2.0.4", "react-json-pretty": "^2.2.0", "react-json-view": "^1.20.2", "react-measure": "^2.3.0", "react-moment": "^1.1.1", "react-qr-code": "^1.0.5", "react-redux": "^7.2.2", "react-ribbons": "^1.0.6", "react-router-dom": "^5.1.2", "react-scripts": "3.4.3", "react-select": "^5.4.0", "react-tooltip": "^4.2.13", "recharts": "^2.0.4", "redux": "^4.0.5", "resolve": "1.12.2", "resolve-url-loader": "^3.1.2", "rrule": "^2.7.1", "sass-loader": "8.0.0", "simple-peer": "^9.10.0", "style-loader": "1.0.0", "terser-webpack-plugin": "2.2.1", "ts-pnp": "1.1.5", "typeface-roboto": "0.0.75", "url-loader": "2.3.0", "webpack": "4.43.0", "webpack-dev-server": "^3.11.2", "webpack-manifest-plugin": "2.2.0", "workbox-webpack-plugin": "4.3.1", "yup": "^0.28.5"}, "overrides": {"launchdarkly-react-client-sdk": {"launchdarkly-js-client-sdk": "3.1.2"}}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "local": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"], "qa": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"], "stage": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "jest": {"projects": [{"displayName": "test", "roots": ["<rootDir>/src"], "collectCoverageFrom": ["src/**/*.{js,jsx,ts,tsx}", "!src/**/*.d.ts"], "setupFiles": ["react-app-polyfill/jsdom", "core-js", "jest-launchdark<PERSON>-mock"], "setupFilesAfterEnv": ["<rootDir>/src/setupTests.js"], "testMatch": ["<rootDir>/src/**/__tests__/**/*.{js,jsx,ts,tsx}", "<rootDir>/src/**/*.{spec,test}.{js,jsx,ts,tsx}"], "testEnvironment": "jest-environment-jsdom-fourteen", "transform": {"^.+\\.(js|jsx|ts|tsx)$": "<rootDir>/node_modules/babel-jest", "^.+\\.css$": "<rootDir>/config/jest/cssTransform.js", "^(?!.*\\.(js|jsx|ts|tsx|css|json)$)": "<rootDir>/config/jest/fileTransform.js"}, "transformIgnorePatterns": ["[/\\\\]node_modules[/\\\\].+\\.(js|jsx|ts|tsx)$", "^.+\\.module\\.(css|sass|scss)$"], "modulePaths": [], "moduleNameMapper": {"^react-native$": "react-native-web", "^.+\\.module\\.(css|sass|scss)$": "identity-obj-proxy", "AppConfig": "<rootDir>/config/dev.json"}, "moduleFileExtensions": ["web.js", "js", "web.ts", "ts", "web.tsx", "tsx", "json", "web.jsx", "jsx", "node"], "watchPlugins": ["jest-watch-typeahead/filename", "jest-watch-typeahead/testname"]}, {"displayName": "lint", "runner": "jest-runner-es<PERSON>", "testMatch": ["<rootDir>/src/**/*.{js,jsx,ts,tsx}"]}]}, "babel": {"presets": [["react-app", {"targets": "> 0.25%, not dead", "useBuiltIns": "entry", "corejs": 3}]], "env": {"production": {"plugins": [["babel-plugin-transform-react-remove-prop-types", {"removeImport": true}], ["@babel/plugin-transform-runtime", {"corejs": 3}]]}}}}