import React from "react";
import { shallow } from "enzyme";
import TablePaginationAction from ".";

describe("Ticket Pagination Actions", () => {
  it("should render without exploding", () => {
    const wrapper = shallow(<TablePaginationAction />);
    expect(wrapper.find("div").exists()).toBe(true);
  });

  it("should return to first page when first page button clicked", () => {
    const spy = jest.fn();
    const wrapper = shallow(<TablePaginationAction onChangePage={spy} />);
    const button = wrapper.findWhere(
      buttons => buttons.props().name === "first page button"
    );
    button.props().onClick();
    expect(spy).toBeCalledTimes(1);
  });

  it("should go to last page when last page button clicked", () => {
    const spy = jest.fn();
    const wrapper = shallow(<TablePaginationAction onChangePage={spy} />);
    const button = wrapper.findWhere(
      buttons => buttons.props().name === "last page button"
    );
    button.props().onClick();
    expect(spy).toBeCalledTimes(1);
  });

  it("should go to next page when next page button clicked", () => {
    const spy = jest.fn();
    const wrapper = shallow(<TablePaginationAction onChangePage={spy} />);
    const button = wrapper.findWhere(
      buttons => buttons.props().name === "next page button"
    );
    button.props().onClick();
    expect(spy).toBeCalledTimes(1);
  });

  it("should go to previous page when next page button clicked", () => {
    const spy = jest.fn();
    const wrapper = shallow(<TablePaginationAction onChangePage={spy} />);
    const button = wrapper.findWhere(
      buttons => buttons.props().name === "previous page button"
    );
    button.props().onClick();
    expect(spy).toBeCalledTimes(1);
  });

  it("should disable next button when there are no more pages to go to", () => {
    const wrapper = shallow(
      <TablePaginationAction page={3} count={27} rowsPerPage={10} />
    );
    const button = wrapper.findWhere(
      buttons => buttons.props().name === "next page button"
    );
    expect(button.props().disabled).toBe(true);
  });

  it("should enable next button when there are more pages to go to", () => {
    const wrapper = shallow(
      <TablePaginationAction page={3} count={57} rowsPerPage={10} />
    );
    const button = wrapper.findWhere(
      buttons => buttons.props().name === "next page button"
    );
    expect(button.props().disabled).toBe(false);
  });

  it("should disable previous page button when on first page", () => {
    const wrapper = shallow(
      <TablePaginationAction page={0} count={9000} rowsPerPage={25} />
    );
    const button = wrapper.findWhere(
      buttons => buttons.props().name === "previous page button"
    );
    expect(button.props().disabled).toBe(true);
  });

  it("should enable previous page button when not on last page", () => {
    const wrapper = shallow(
      <TablePaginationAction page={7} count={240} rowsPerPage={25} />
    );
    const button = wrapper.findWhere(
      buttons => buttons.props().name === "next page button"
    );
    expect(button.props().disabled).toBe(false);
  });
});
