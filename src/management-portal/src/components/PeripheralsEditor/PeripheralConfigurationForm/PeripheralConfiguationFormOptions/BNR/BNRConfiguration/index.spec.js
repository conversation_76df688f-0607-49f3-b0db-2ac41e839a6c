import React from "react";
import { render, waitFor, screen } from "@testing-library/react";
import { BNRConfiguration, ConfigurationTableInput } from "./index";
import Guid from "devextreme/core/guid";
import useHubContext from "../../../../../../hooks/useHubContext";
import { useSnackbar } from "notistack";
import { useFormik } from "formik";
import {Button, Table, TableBody, TableRow} from "@material-ui/core";
import { mount, shallow } from "enzyme";
import DeviceService from "../../../../../../services/DeviceService";

jest.mock("../../../../../../hooks/useHubContext");
jest.mock("notistack");
jest.mock("formik");

describe("BNR Configuration components", () => {
  const classes = jest.fn();
  const enqueueSnackbar = jest.fn();
  const submitForm = jest.fn();
  const handleChange = jest.fn();
  const dirty = true;
  const handleBlur = jest.fn();
  const resetForm = jest.fn();
  const subscribe = jest.fn();
  const unsubscribe = jest.fn();
  const invoke = jest.fn();
  const submitSpy = jest.fn();


  beforeEach(() => {
    jest.clearAllMocks();

    useHubContext.mockReturnValue({
      portalHub: {
        subscribe: subscribe,
        unsubscribe: unsubscribe,
        invoke: invoke,
      },
    });

    useSnackbar.mockReturnValue({
      enqueueSnackbar: jest.fn(),
    });

    useFormik.mockReturnValue({
      isValid: true,
      submitForm: submitForm,
      handleChange: handleChange,
      dirty: dirty,
      handleBlur: handleBlur,
      resetForm: resetForm,
      errors: null,
      values: {
        recyclerConfigurations: recyclerConfigExample.recyclerConfigurations
      },
    });

  });

  it("should render BNR Configuration without blowing up", () => {
    const wrapper = shallow(<BNRConfiguration entityid={new Guid()} override={false}/>);

    const primaryButton = wrapper.find(Button);
    const table = wrapper.find(Table);
    const tableBody = wrapper.find(TableBody);

    expect(wrapper.exists()).toBe(true);
    expect(primaryButton.exists()).toBe(true);
    expect(table.exists()).toBe(true);
    expect(tableBody.exists()).toBe(true);
  });

  it("should submit new recycler configurations when button pressed", async () => {
    DeviceService.prototype.configureBNR = submitSpy;
    const deviceService = jest.fn(() => {
      return { configureBNR: submitSpy }
    });
    render(<BNRConfiguration entityid={new Guid()} deviceService={deviceService} override={false}/>);
    const button = await screen.findByRole("button");

    await waitFor(() => {
      button.click();
    });

    await waitFor(() => {
      expect(submitForm).toHaveBeenCalledTimes(1);
    });
  });

  it("should render configuration table input without blowing up", () => {
    const wrapper = shallow(
      <ConfigurationTableInput
        recyclerConfigs={recyclerConfigExample}
        classes={classes}
        enqueueSnackbar={enqueueSnackbar}
      />
    );
    const rows = wrapper.find(TableRow);

    expect(wrapper.exists()).toBe(true);
    expect(rows.length).toBe(4);
  });
});

const recyclerConfigExample = {
  recyclerConfigurations: [{
      RecyclerName: "RE3",
      Denomination: 10,
      LowThreshold: 0,
      HighThreshold: 20,
      FullThreshold: 30,
      BillCount: 0,
    }, {
      RecyclerName: "RE4",
      Denomination: 1,
      LowThreshold: 0,
      HighThreshold: 40,
      FullThreshold: 60,
      BillCount: 5,
    }, {
      RecyclerName: "RE5",
      Denomination: 20,
      LowThreshold: 0,
      HighThreshold: 20,
      FullThreshold: 30,
      BillCount: 0,
    }, {
      RecyclerName: "RE6",
      Denomination: 5,
      LowThreshold: 0,
      HighThreshold: 50,
      FullThreshold: 60,
      BillCount: 0,
    }
  ]
};