import React, { useState, useEffect } from "react";
import PropTypes from "prop-types";
import { useForm, Controller, watch } from "react-hook-form";
import {
  Typo<PERSON>,
  Grid,
  Button,
  Divider,
  FormControlLabel,
  Switch,
  Select,
  FormControl,
  InputLabel,
  Box,
  Tooltip,
} from "@material-ui/core";
import clsx from "clsx";
import { useStyles } from "./styles";
import apiClient from "../../auth/apiClient";
import EntityService from "../../services/EntityService";
import { useEnqueueSnackbar } from "../../hooks/useEnqueueSnackbar";
import { ENTITY_TYPE, DEFAULT_THEME, DARK_THEME } from "../../constants";
import useCurrentUser from "../../hooks/useCurrentUser";
import useCurrentFacility from "../../hooks/useCurrentFacility";
import useUserPreferences from "../../hooks/useUserPreferences/useUserPreferences";
import { useSelector } from "react-redux";
import FacilityDropdownMenu from "./FacilityDropdownMenu";

const UserPreferences = ({ onClose }) => {
  const classes = useStyles();
  const enqueueSnackbar = useEnqueueSnackbar();
  const entityService = new EntityService(apiClient);
  const currentUser = useCurrentUser();
  const { facilityID: currentFacilityID } = useCurrentFacility();
  const savedTheme = useSelector((state) => state.userPreferences.theme);
  const savedLandedFacility = useSelector((state) => state.userPreferences.landedFacility);
  const { savePreferences } = useUserPreferences();

  const { control, handleSubmit, setValue, reset, watch } = useForm({
    defaultValues: {
      theme: DEFAULT_THEME,
      facilityPreference: "",
    },
    mode: "onChange",
  });

  const watchedValues = watch();
  const [isSaving, setIsSaving] = useState(false);
  const [hierarchicalFacilities, setHierarchicalFacilities] = useState([]);
  const [loadingFacilities, setLoadingFacilities] = useState(true);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  useEffect(() => {
    const newValues = {
      theme: savedTheme || DEFAULT_THEME,
      facilityPreference: savedLandedFacility || "",
    };
    reset(newValues);
  }, [savedTheme, savedLandedFacility]);

  useEffect(() => {
    const loadFacilities = async () => {
      try {
        setLoadingFacilities(true);
        const result = await entityService.getPropertiesByScopes();

        if (result?.status === 200) {
          const hierarchicalData = result.data || [];
          setHierarchicalFacilities(hierarchicalData);

          const totalFacilities = getTotalSelectableFacilities();
          
          if (totalFacilities === 1 && !savedLandedFacility) {
            const singleFacilityId = findSingleFacilityId(hierarchicalData);
            if (singleFacilityId) {
              setValue("facilityPreference", singleFacilityId);
            }
          } else if (
            hierarchicalData.length > 0 &&
            !savedLandedFacility &&
            currentFacilityID
          ) {
            if (
              isCurrentFacilityAvailable(currentFacilityID, hierarchicalData)
            ) {
              setValue("facilityPreference", currentFacilityID);
            }
          }
        } else {
          enqueueSnackbar("We ran into a problem while getting facilities", {
            variant: "error",
            tag: "fetchFacilitiesError",
          });
        }
      } catch (error) {
        console.error("Failed to load facilities:", error);
        enqueueSnackbar("We ran into a problem while getting facilities", {
          variant: "error",
          tag: "fetchFacilitiesError",
        });
      } finally {
        setLoadingFacilities(false);
      }
    };

    loadFacilities();
  }, [currentUser, currentFacilityID, savedLandedFacility]);

  const getTotalSelectableFacilities = () => {
    return (hierarchicalFacilities || [])
      .flatMap(parent => parent.children || [])
      .reduce((count, child) => {
        if (child.entityType === ENTITY_TYPE.FacilityGroup) {
          return count + (child.children?.length || 0);
        }
        return count + 1;
      }, 0);
  };

  const getFacilityNameById = (facilityId) => {
    if (!facilityId) {
      return "";
    }

    return (hierarchicalFacilities || [])
      .flatMap(parent => parent.children || [])
      .flatMap(child => [child, ...(child.children || [])])
      .find(facility => facility.id === facilityId)?.name || "";
  };

  const isCurrentFacilityAvailable = (currentFacilityID, hierarchicalData) => {
    return (hierarchicalData || [])
      .flatMap(parent => parent.children || [])
      .flatMap(child => [child, ...(child.children || [])])
      .some(facility => facility.id === currentFacilityID);
  };

  const findSingleFacilityId = (hierarchicalData) => {
    return (hierarchicalData || [])
      .flatMap(parent => parent.children || [])
      .map(child => {
        if (child.entityType === ENTITY_TYPE.FacilityGroup) {
          return child.children?.[0]?.id || "";
        }
        return child.id;
      })
      .find(id => id) || "";
  };

  const onSubmit = async (formData) => {
    setIsSaving(true);
    try {
      if (currentUser?.UserID) {
        savePreferences(currentUser.UserID, {
          theme: formData.theme,
          landedFacility: formData.facilityPreference,
        });

        enqueueSnackbar("User preferences saved successfully", {
          variant: "success",
          tag: "savePreferencesSuccess",
        });
      }
      onClose();
    } catch (error) {
      console.error("Failed to save preferences:", error);
      enqueueSnackbar("Failed to save user preferences", {
        variant: "error",
        tag: "savePreferencesError",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    onClose();
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className={classes.container}>
      <div className={classes.header}>
        <Typography
          variant="h5"
          color="primary"
          component="h2"
          className={classes.title}
        >
          User Preferences
        </Typography>
        <Divider className={classes.divider} />
      </div>
      <div className={classes.content}>
        <div className={classes.section}>
          <Typography variant="h6" className={classes.sectionTitle}>
            Theme
          </Typography>
          <Divider className={classes.sectionDivider} />
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Controller
                name="theme"
                control={control}
                render={({ field }) => (
                  <Tooltip
                    title="Your theme preference when you log into the Portal"
                    arrow
                    placement="bottom-start"
                  >
                    <FormControlLabel
                      control={
                        <Switch
                          checked={field.value === DARK_THEME}
                          onChange={(event) => {
                            const newTheme = event.target.checked ? DARK_THEME : DEFAULT_THEME;
                            field.onChange(newTheme);
                          }}
                          color="primary"
                        />
                      }
                      label="Enable Dark Mode"
                      labelPlacement="end"
                    />
                  </Tooltip>
                )}
              />
            </Grid>
          </Grid>
        </div>

        <div className={classes.section}>
          <Typography variant="h6" className={classes.sectionTitle}>
            Facility Preference
          </Typography>
          <Divider className={classes.sectionDivider} />
          <Grid container spacing={3}>
            <Grid item xs={12}>
              {getTotalSelectableFacilities() === 1 ? (
                <Tooltip
                  title="This is your only facility"
                  arrow
                  placement="bottom-start"
                >
                  <Box>
                    <Typography
                      variant="body1"
                      className={classes.singleFacilityText}
                    >
                      {getFacilityNameById(watchedValues.facilityPreference)}
                    </Typography>
                  </Box>
                </Tooltip>
              ) : (
                <Tooltip
                  title="Your facility landing preference when logging in"
                  arrow
                  placement="bottom-start"
                  disableHoverListener={isDropdownOpen}
                >
                  <Box>
                    <Controller
                      name="facilityPreference"
                      control={control}
                      render={({ field }) => (
                        <FormControl fullWidth disabled={loadingFacilities}>
                          <InputLabel>Facilities and Areas</InputLabel>
                          <Select
                            {...field}
                            open={isDropdownOpen}
                            onOpen={() => setIsDropdownOpen(true)}
                            onClose={() => setIsDropdownOpen(false)}
                            label="Facilities and Areas"
                            displayEmpty
                            renderValue={(selected) => getFacilityNameById(selected)}
                            MenuProps={{
                              PaperProps: {
                                className: classes.dropdownPaper,
                              },
                              anchorOrigin: {
                                vertical: "bottom",
                                horizontal: "left",
                              },
                              transformOrigin: {
                                vertical: "top",
                                horizontal: "left",
                              },
                              getContentAnchorEl: null,
                            }}
                          >
                            <FacilityDropdownMenu
                              hierarchicalFacilities={hierarchicalFacilities}
                              loadingFacilities={loadingFacilities}
                              classes={classes}
                              onFacilitySelect={(facilityId) => {
                                setIsDropdownOpen(false);
                                field.onChange(facilityId);
                              }}
                            />
                          </Select>
                        </FormControl>
                      )}
                    />
                  </Box>
                </Tooltip>
              )}
            </Grid>
          </Grid>
        </div>
      </div>

      <Box className={classes.bottomButtons}>
        <Divider />
        <div className={classes.buttonWrapper}>
          <div className={classes.buttonContainer}>
            <Button
              disabled={isSaving}
              color="primary"
              name="save"
              variant="contained"
              className={clsx("saveBtn", classes.saveButton)}
              type="submit"
            >
              Save
            </Button>
            <Button
              className={clsx("closeBtn", classes.closeButton)}
              name="close"
              variant="contained"
              onClick={handleCancel}
              type="button"
            >
              Close
            </Button>
          </div>
        </div>
      </Box>
    </form>
  );
};

UserPreferences.propTypes = {
  onClose: PropTypes.func.isRequired,
};

export default UserPreferences;
