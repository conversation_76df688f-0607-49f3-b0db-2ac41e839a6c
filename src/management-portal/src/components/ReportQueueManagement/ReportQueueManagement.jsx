import React, { useEffect, useState, useCallback, useRef } from "react";
import clsx from "clsx";
import { Grid, Divider, IconButton } from "@material-ui/core";
import Pagination from "@material-ui/lab/Pagination";
import Skeleton from "@material-ui/lab/Skeleton";
import { useSelector } from "react-redux";

import ReportQueueManagementTable from "./ReportQueueManagementTable/ReportQueueManagementTable";
import Title from "../Title";
import useWindowDimensions from "../../hooks/useWindowDimensions";

// Styles
import { useStyles } from "./ReportQueueManagement.styles";

import {
  Switch,
  FormControlLabel,
} from "@material-ui/core";

import { useEnqueueSnackbar } from "../../hooks/useEnqueueSnackbar";

const ReportQueueManagement = () => {
  const classes = useStyles();
  const { height } = useWindowDimensions();

  // State management
  const [currentPage, setCurrentPage] = useState(1);

  const [results, setResults] = useState([]);
  const [totalCount, setTotalCount] = useState(0);
  // The user cannot set the number of records per page.  It is hardcoded to 10 per AO-24285
  const itemLimit = 10;
  
  const requests = useSelector((state) => state.reportQueue.requests);
  // use loading from the slice to more accurately reflect when the data is loading.
  const loading = useSelector((state) => state.reportQueue.loading);
  const error = useSelector((state) => state.reportQueue.error);

  const [isChecked, setIsChecked] = useState(false);

  const handleToggle = () => {
    setCurrentPage(1);
    setIsChecked(!isChecked);
  };
  // Get the current date
const currentDate = new Date();
const enqueueSnackbar = useEnqueueSnackbar();

function expired(value) {
      var date = new Date(value);
      // completed and error records expire in 3 days
      date.setDate(date.getDate() + 3);

    return date < currentDate; 
}

  function filteredRequestData() {
        return requests.filter((item) => item.status === "Queued" || item.status === "Processing" || 
    ((item.status === "Completed" || item.status === "Error") && !expired(item.queued)));
    }

  useEffect(() => { 
    // If loading or no requests, don't proceed
    if (loading || !requests) return;

    // Handle error state
    if (error) {
      enqueueSnackbar("Unable to load the queue listing, please try again later.", {
        variant: "error",
        tag: "FailedToLoadReportQueue",
      });
      return;
    }

  // Filter the requests based on active status
  const getFilteredRequests = () => {
    let filteredRequests = filteredRequestData();
    if (isChecked) {
      filteredRequests = filteredRequests.filter(
        (item) => item.status !== "Completed" && item.status !== "Error"
      );
    }
    return filteredRequests;
  };

  const filteredRequests = getFilteredRequests();

    // Update the state with the filtered results and count
    setResults(filteredRequests);
    setTotalCount(filteredRequests.length);
  }, [requests, isChecked, loading, error]);
  
  
  return (
    <Grid container className={clsx(classes.root)}>
      <Grid item xs={12} className={clsx(classes.header)}>
        <Title className={classes.title}>Report Queue Management</Title>
      </Grid>
      <Grid container className={clsx(classes.infoContainer)}>
        <div className={clsx(["toggle-btn-header", classes.headerButtons])}>
            <FormControlLabel
              control={
                <Switch
                  onChange={handleToggle}
                  name="checkedB"
                  color="primary"
                />
              }
              label={"Only Show Active"}
            />
          </div>
        <Grid item xs={12}>
          {loading ? (
            <div className={clsx(classes.loadingWrapper)}>
              {[...Array(3)].map((_, index) => (
                <Skeleton
                  key={index}
                  className={classes.skeleton}
                  animation="wave"
                />
              ))}
            </div>
          ) : (
            <>
              <Divider className={classes.resultsDivider} />
              {totalCount === 0 && (
                <div
                  className={clsx([
                    "animate__animated",
                    "animate__flipInX",
                    "results",
                    "not-found",
                    classes.noResults,
                  ])}
                >
                  <span className={clsx(["no-results-text"])}>
                    No report queue records
                  </span>
                </div>
              )}
              {totalCount > 0 && (
                <Grid
                  container
                  item
                  spacing={2}
                  xs={12}
                  className={clsx(classes.tableContainer)}
                >
                  {Math.ceil(totalCount / itemLimit) > 1 && (
                  <Pagination
                    className={clsx(classes.pagination)}
                    count={Math.ceil(totalCount / itemLimit)}
                    onChange={(_, value) => setCurrentPage(value)}
                    page={currentPage}
                    shape="rounded"
                    color="primary"
                  /> )}             
                  <ReportQueueManagementTable
                    results={results.slice(
                      (currentPage - 1) * itemLimit,
                      currentPage * itemLimit
                    )}
                  />
                </Grid>
              )}
            </>
          )}
        </Grid>
      </Grid>
    </Grid>
  );
};

export default ReportQueueManagement;
