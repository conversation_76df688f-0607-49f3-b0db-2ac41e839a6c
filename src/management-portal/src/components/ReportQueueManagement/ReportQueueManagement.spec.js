import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import ReportQueueManagement from "./ReportQueueManagement";
import { useSelector } from "react-redux";
import {
  createMuiTheme,
  ThemeProvider,
  lighten,
  darken,
} from "@material-ui/core";

import { act } from "react-dom/test-utils";

jest.mock("react-redux", () => ({
  ...jest.requireActual("react-redux"),
  useSelector: jest.fn(),
}));

jest.mock("../../hooks/useEnqueueSnackbar");

jest.mock("./ReportQueueManagement.styles", () => ({
  useStyles: jest.fn(() => ({
    root: "root-class",
    header: "header-class",
    title: "title-class",
    loadingWrapper: "loading-class",
    skeleton: "skeleton-class",
    resultsDivider: "divider-class",
    noResults: "no-results-class",
    pagination: "pagination-class",
    tableContainer: "table-container-class",
  })),
}));

jest.mock("@material-ui/core/styles", () => ({
  ...jest.requireActual("@material-ui/core/styles"),
  lighten: jest.fn(() => "#A3C9F1"),
  darken: jest.fn(() => "#B24A4A"),
}));

const MockTheme = ({ children }) => {
  const theme = createMuiTheme({
    palette: {
      amano: {
        base: {
          primary: {
            main: "rgb(0, 107, 166)",
          },
          secondary: {
            main: "rgb(0, 107, 166)",
            light: lighten("rgb(0, 107, 166)", 0.2),
          },
        },
        warnings: {
          fire: darken("#FE626B", 0.3),
        },
      },
    },
  });
  return <ThemeProvider theme={theme}>{children}</ThemeProvider>;
};

const mockRequests = [
  {
    id: 1,
    reportname: "Access Groups",
    queued: new Date().toISOString(),
    status: "Processing",
    by: "Bob Evans",
    action: "View",
  },
  {
    id: 2,
    reportname: "General Totals",
    queued: new Date().toISOString(),
    status: "Completed",
    by: "Alice Johnson",
    action: "View",
  },
];
const mockRequestsEmpty = [];

describe("ReportQueueManagement Component", () => {
  const activeSearch = {
    removeRequestAfter: 3,
    requestScope: "All",
    numberOfRequests: 2,
  };

  beforeEach(() => {
    // Correctly mock requestFullscreen and exitFullscreen
    Object.defineProperty(document.documentElement, "requestFullscreen", {
      writable: true,
      value: jest.fn(),
    });
    Object.defineProperty(document, "exitFullscreen", {
      writable: true,
      value: jest.fn(),
    });

    useSelector.mockImplementation((callback) =>
      callback({
        reportQueue: {
          requests: mockRequests,
          activeSearch,
        },
        entities: {
          Context: {
            details: {
              timezone: "America/New_York",
            },
          },
        },
      })
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("renders table and displays with no rows", async () => {
    useSelector.mockImplementation((callback) =>
      callback({
        reportQueue: {
          requests: mockRequestsEmpty,
          activeSearch,
        },
        entities: {
          Context: {
            details: {
              timezone: "America/New_York",
            },
          },
        },
      })
    );

    render(
      <MockTheme>
        <ReportQueueManagement />
      </MockTheme>
    );

    expect(screen.getByText("Report Queue Management")).toBeInTheDocument();
    expect(screen.getByText("No report queue records")).toBeInTheDocument();
  });

  it("renders the table headers correctly", () => {
    render(
      <MockTheme>
        <ReportQueueManagement />
      </MockTheme>
    );

    expect(screen.getByText("Report Name")).toBeInTheDocument();
    expect(screen.getByText("Queued")).toBeInTheDocument();
    expect(screen.getByText("Status")).toBeInTheDocument();
    expect(screen.getByText("By")).toBeInTheDocument();
    expect(screen.getByText("Action")).toBeInTheDocument();
  });

  it("renders rows with provided results data", async () => {
    render(
      <MockTheme>
        <ReportQueueManagement />
      </MockTheme>
    );

    // Check if data is rendered correctly
    const rows = screen.getAllByTestId("reportqueue-row");
    expect(rows).toHaveLength(2);
    expect(screen.getByText("Access Groups")).toBeInTheDocument();
    expect(screen.getByText("Bob Evans")).toBeInTheDocument();
    expect(screen.getByText("Processing")).toBeInTheDocument();
    expect(screen.getByText("Alice Johnson")).toBeInTheDocument();
  });
 
});
