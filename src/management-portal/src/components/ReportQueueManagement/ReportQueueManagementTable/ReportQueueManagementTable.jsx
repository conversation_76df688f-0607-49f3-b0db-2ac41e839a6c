import React from "react";
import clsx from "clsx";
import { useStyles } from "./ReportQueueManagementTable.styles";
import moment from "moment";
import '../../ResponsiveTable/_responsive.table.css';

const ReportQueueManagementTable = ({ results = [] }) => {
const classes = useStyles();

function getExpirationMessage(status,queuedDate) {
  return status === "Processing" ||  status === "Queued" ? "" : "     Expires " + getExpirationDays(queuedDate); 
}

function getExpirationDays(queuedDate) {
  if(!queuedDate) return;
  var date = new Date(queuedDate);
  
  const expiresDate = new Date(date);
  // completed and error records expire in 3 days
  expiresDate.setDate(date.getDate() + 3);

  return moment(expiresDate).fromNow();
}

  return (
    <div className={classes.tableContainer}>  
    <table className={clsx("a1-responsive")}>     
        <thead className={clsx(classes.thHeader)}>
          <tr>
            <th className={clsx(classes.resultHeader)}>Report Name</th>
            <th className={clsx(classes.centerHeader)}>Queued</th>
            <th className={clsx(classes.centerHeader)}>Status</th>
            <th className={clsx(classes.centerHeader)}>By</th>
            <th className={clsx(classes.centerHeader)}>Action</th>
          </tr>
        </thead>

        <tbody>
          {results.map((result) =>  {

            return (
            <tr
              key={result.id}
              data-testid="reportqueue-row"
            >
              <td
                data-column="Report Name"
              >                           
                <span className={clsx([classes.left])}>{result.reportname}</span>              
                <span className={clsx([classes.expired])}>{getExpirationMessage(result.status,result.queued)}</span>                
                 </td>
              <td
                data-column="Queued"
                className={clsx([classes.centerAlign])}
              >
                  {result.queued}
                </td>
                 <td
                data-column="Status"
                className={clsx([classes.status, result?.status.toLowerCase()])}>
                  {result.status}
                </td>
             <td
                data-column="By"
                className={clsx([classes.centerAlign])}
              >
                  {result.by}
                </td>
              <td
                data-column="Action"
                className={clsx([classes.centerAlign])}
              >
                  {result.action}
                </td>     
              </tr>
            );
          })}
        </tbody>
      </table>
    </div>
  );
};

export default ReportQueueManagementTable;
