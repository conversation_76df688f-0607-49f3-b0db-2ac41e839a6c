import React from "react";
import { render, screen } from "@testing-library/react";
import ReportQueueManagementTable from "./ReportQueueManagementTable";
import useCurrentFacilityTimezone from "../../../hooks/useCurrentFacilityTimezone";
import { useStyles } from "./ReportQueueManagementTable.styles";
import {
  createMuiTheme,
  ThemeProvider,
  lighten,
  darken,
} from "@material-ui/core";

jest.mock("./ReportQueueManagementTable.styles");
jest.mock("../../../hooks/useCurrentFacilityTimezone");

const MockTheme = ({ children }) => {
  const theme = createMuiTheme({
    palette: {
      amano: {
        base: {
          primary: { main: "rgb(0, 107, 166)" },
          secondary: {
            main: "rgb(0, 107, 166)",
            light: lighten("rgb(0, 107, 166)", 0.2),
          },
        },
        warnings: {
          fire: darken("#FE626B"),
        },
      },
    },
  });
  return <ThemeProvider theme={theme}>{children}</ThemeProvider>;
};

describe("ReportQueueManagementTable Component", () => {
  beforeEach(() => {
    useCurrentFacilityTimezone.mockReturnValue({
      timeZone: "America/Los_Angeles",
    });
    useStyles.mockReturnValue({});
  });

  it("should render the table headers correctly", () => {
    render(
      <MockTheme>
        <ReportQueueManagementTable results={[]} />
      </MockTheme>
    );
    expect(screen.getByText("Report Name")).toBeInTheDocument();
    expect(screen.getByText("Queued")).toBeInTheDocument();
    expect(screen.getByText("Status")).toBeInTheDocument();
    expect(screen.getByText("By")).toBeInTheDocument();
    expect(screen.getByText("Action")).toBeInTheDocument();
  });

  it("should render rows with provided results data", async () => {
    const resultsData = [
      {
        reportname: "Access Groups",
        queued: new Date().toISOString(),
        status: "Processing",
        by: "Bob Evans",
        action: "Completed",
      },
    ];

    render(
      <MockTheme>
        <ReportQueueManagementTable results={resultsData} />
      </MockTheme>
    );

    // Check if data is rendered correctly
    expect(screen.getByText("Access Groups")).toBeInTheDocument();
    expect(screen.getByText("Processing")).toBeInTheDocument();
    expect(screen.getByText("Bob Evans")).toBeInTheDocument();
    expect(screen.getByText("Completed")).toBeInTheDocument();
  });
});
