import { makeStyles } from "@material-ui/core/styles";
import { green } from "@material-ui/core/colors";

export const useStyles = makeStyles((theme) => ({
  tableContainer: {
    width: "100%",
    display: "flex",
    justifyContent: "center",
    [theme.breakpoints.down('sm')]: {
      overflowX: "auto",
      justifyContent: "flex-start",
    }
  },
  resultTable: {
    margin: "10px 0 25px 0 !important",
    width: "calc(100% - 50px)",
    minWidth: "800px",
    borderCollapse: "collapse",
    border: `1px solid ${theme.palette.grey.main}`,
    tableLayout: "fixed",
    "& th, & td": {
      borderBottom: `1px solid ${theme.palette.grey.main}`,
      borderRight: `1px solid ${theme.palette.grey.main}`,
      padding: "8px",
    }
  },
  thHeader: {
    backgroundColor: theme.palette.header,
    color: theme.palette.amano.base.main,
    fontWeight: "bold",
  },
  centerAlign: {
    textAlign: "center",
  },
  checkIcon: {
    color: theme.palette.success.main,
  },
  centerHeader: {
    textAlign: "center !important",
    verticalAlign: "middle",
  },
  status: {
    fontWeight:"bold",
    textAlign: "center",
    "&.processing": { 
      color: theme.palette.amano.base.primary.main,
    },
    "&.error" : {
      color: theme.palette.error.dark
    },
    "&.completed" : {
      color: green[500]
    },
    "&.queued" : {
      color: theme.palette.text.disabled,
      fontWeight:"bold"
    }
  },
  expired: {
    color: theme.palette.text.disabled,
    fontStyle:"italic",
    display: "inline-block",
    float: "right"
  },
   left: {
    display: "inline-block",
    float: "left"
  },

}));
