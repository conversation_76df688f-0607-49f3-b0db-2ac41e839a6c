import React, { useState, useEffect } from "react";
import { useStyles, StyledTableCell } from "./styles";
import { Container, Typography } from "@material-ui/core";
import Table from "@material-ui/core/Table";
import TableBody from "@material-ui/core/TableBody";
import TableCell from "@material-ui/core/TableCell";
import TableHead from "@material-ui/core/TableHead";
import TableRow from "@material-ui/core/TableRow";
import useHubContext from "../../hooks/useHubContext";
import { NON_RESET_COUNTS_TOPIC, NON_RESET_COUNTS_REQUEST_TOPIC, PORTAL_TRIGGER } from "../../constants";

export const DeviceSerialInformation = ({ entityid }) => {
  const classes = useStyles();
  const { portalHub } = useHubContext();
  const [serials, setSerials] = useState(serialStarter);


  useEffect(() => {
    // Subscribe to money inventory topic prefixed with this entityId
    portalHub.subscribe(
      `${entityid}_${NON_RESET_COUNTS_TOPIC}`,
      (message) => {
        var data = JSON.parse(message);
        var message = JSON.parse(data.Message);
        setSerials(message.serialNumbers);
      }
    );

    // On render invoke money inventory request
    portalHub.invoke(PORTAL_TRIGGER, {
      entityID: entityid,
      topic: NON_RESET_COUNTS_REQUEST_TOPIC,
      method: "",
      action: "NonResetCountsRequest",
      message: "",
    });
    
    return () => {
      portalHub.unsubscribe(`${entityid}_${NON_RESET_COUNTS_TOPIC}`);
    };
  }, [portalHub.connection, portalHub.isOpen]);


  return (
    <Container className={classes.tablecontainer}>
      <Table data-id={`cash-status-${entityid}`} className={classes.table} aria-label="spanning table">
        <TableHead>
          <TableRow>
            <TableCell align="left" colSpan={4} style={{ alignItems: "left", textAlign: "left" }}>
              <Typography color="primary">Serial Information</Typography>
            </TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          <TableRow>
            <TableCell className={classes.subheader}>Item</TableCell>
            <TableCell align="right"  className={classes.subheader}>Make</TableCell>
            <TableCell align="right"  className={classes.subheader}>Model</TableCell>
            <TableCell align="right"  className={classes.subheader}>Serial</TableCell>
          </TableRow>

          {serials?.map((serial) => (
            <TableRow key={`serial-${serial.Item}`} style={{ borderTopWidth: '0px' }}>
              <StyledTableCell style={{fontWeight:"bold"}}>{serial.Item}</StyledTableCell>
              <StyledTableCell align="right" >{serial.Make}</StyledTableCell>
              <StyledTableCell align="right" >{serial.Model}</StyledTableCell>
              <StyledTableCell align="right" >{serial.Serial}</StyledTableCell>
            </TableRow>
          ))}

        </TableBody>
      </Table>
    </Container>
  )
}

export default DeviceSerialInformation;

export const serialStarter = [
  {
    Item: "Device",
    Make:"",
    Model: "",
    Serial: "1234567"
  },
  {
    Item: "Daemon",
    Make:"",
    Model: "",
    Serial: "1.0.1"
  },
  {
    Item: "Agent",
    Make:"",
    Model: "",
    Serial: "0.5"
  },    
  {
    Item: "BNR",
    Make:"ABC123",
    Model: "XYZ789",
    Serial: "DEF123.2"
  },  
  {
    Item: "Printer",
    Make:"PyramidTech",
    Model: "WhiteAndBlue",
    Serial: "BB994433"
  },

];