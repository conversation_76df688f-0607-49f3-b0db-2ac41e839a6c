import React from "react";
import { render, screen, waitFor} from "@testing-library/react";
import HotelRatesConfiguration from "./HotelRatesConfiguration";
import { useSnackbar } from "notistack";
import { useConfirmationDialog } from "../../hooks/useConfirmationDialog";
import AccessGroupService from "../../services/AccessGroupService";
import { useSelector } from "react-redux";

jest.mock("../../hooks/useConfirmationDialog");
jest.mock("../../hooks/useEnqueueSnackbar")
jest.mock("notistack");

jest.mock("../Forms/Settings/index", () => ({
  useSettingDispatchChange: () => jest.fn(),
}));
jest.mock("../../hooks/useCurrentFacility", () =>
  jest.fn().mockReturnValue({ facilityID: 1 })
);
jest.mock("../../services/AccessGroupService", () => jest.fn());
jest.mock("../../auth/apiClient")
jest.mock("react-redux", () => ({
  ...jest.requireActual("react-redux"),
  useSelector: jest.fn(() => {
    return {
      entities: {
        Context: {
          settings: {
            preferences: {
              settingname: "test",
            },
          },
        },
      },
    };
  }),
}));

const mockStoreStateForFG = {
  entities: {
    ContextID: 1,
  },
  entityScope: {
    facilityGroupId : undefined,
  },
  user: {
    EmailAddress: "test.com",
},
};

describe("HotelRates Configuration", () => {
  const settingValue = '{"connectiontype":"TCP","port":7777,"ipaddress":"***********","stopbits":2,"databits":7,"parity":"odd","baudrate":9600,"hoteltype":"Opera","serialport":"/dev/ttyUSB0","daysuntilcardpurge":60,"deactivationtime":"0:0","defaultiostatus":"neutral","accessgroupid":"","rates":[{"rateid":1,"name":"R1","ratecode":"A1","fee":5,"accessgroupid":"1"},{"rateid":2,"name":"R2","ratecode":"A2","fee":10,"accessgroupid":"2"}]}'
  beforeEach(() => {
    
    useSnackbar.mockReturnValue({
      enqueueSnackbar: jest.fn(),
      closeSnackbar: jest.fn(),
    });
    useConfirmationDialog.mockReturnValue({
      openDialog: jest.fn(),
      openTextConfirmationDialog: jest.fn(),
    });

    AccessGroupService.prototype.GetAccessGroups = jest
      .fn()
      .mockImplementation(() =>
        Promise.resolve({
          status: 200,
          data: {
            collection: [
              { accessGroupID: "1", name: "AG1" , type:"Normal"},
              { accessGroupID: "2", name: "AG2",type:"Normal" },
            ],
            totalCount:2,
          },
          
        })
      );
    useSelector.mockImplementation((callback) => callback(mockStoreStateForFG));
  });

  
  it("should render empty list with heading and add button", async () => {
   render(
      <HotelRatesConfiguration
        deviceID={"1"}
        hotelInterfaceSettings={"{}"}
      />
    );
    expect(
      await screen.findByRole("heading", { name: /hotel rates/i })
    ).toBeInTheDocument();
    expect(await screen.findByText(/add/i)).toBeInTheDocument();
  
  });


  it("should render rate list", async () => {
    render(
       <HotelRatesConfiguration
         deviceID={"1"}
         hotelInterfaceSettings={settingValue}
       />
     );
   
     expect(
       await screen.findByRole('cell', { name: /r1/i })
     ).toBeInTheDocument();
 
     expect(
       await screen.findByRole('cell', { name: /a1/i })
     ).toBeInTheDocument();
 
     expect(
       await screen.findByRole('cell', { name: /ag1/i })
     ).toBeInTheDocument();
         
     expect(await screen.findByText(/5/i)).toBeInTheDocument();
   
   });


  it("should not render rate list", async () => {
   
    render(
      <HotelRatesConfiguration
        deviceID={"1"}
        hotelInterfaceSettings={"{}"}
      />
    );
    await waitFor(()=> {
      expect(screen.queryByRole('cell', { name: /r1/i })).not.toBeInTheDocument();
    });
    await waitFor(()=> {
      expect(screen.queryByRole('cell', { name: /a1/i })).not.toBeInTheDocument();
    });
    await waitFor(()=> {
      expect(screen.queryByRole('cell', { name: /ag1/i })).not.toBeInTheDocument();
    });
   });

   it("should render edit and delete actions", async () => {
    render(
       <HotelRatesConfiguration
         deviceID={"1"}
         hotelInterfaceSettings={settingValue}
       />
     );

     await waitFor(() => {
       expect(screen.getAllByRole("button", { name: /edit/i })).toHaveLength(2);
     });
     await waitFor(() => {
      expect(screen.getAllByRole("button", { name: /delete/i })).toHaveLength(2);
    });
   
   });
 
});
