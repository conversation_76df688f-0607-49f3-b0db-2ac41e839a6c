import React from "react";
import { render, screen, waitFor, fireEvent } from "@testing-library/react";
import HotelRatesMappingTable from "./HotelRatesMappingTable";

describe("rate list tests", () => {

  const mockRateList = [
    {
      rateid: 1,
      name: "rate1",
      ratecode: "A0",
      fee: 1.1,
      accessgroupid: "12341",
    },
    {
      rateid: 2,
      name: "rate2",
      ratecode: "A1",
      fee: 5.1,
      accessgroupid: "12342",
    },
    {
      rateid: 3,
      name: "rate3",
      ratecode: "A2",
      fee: 3.1,
      accessgroupid: "12343",
    },
    {
      rateid: 4,
      name: "rate4",
      ratecode: "A3",
      fee: 4.1,
      accessgroupid: "12344",
    },
  ];

  const moockAccessGroups = [
    { accessGroupID: "12341", name: "g1" },
    { accessGroupID: "12344", name: "g2" },
  ];
 

  it("should render rate list", async () => {
   
    render(
      <HotelRatesMappingTable
        hotelRatesList={mockRateList}
        accessGroups={moockAccessGroups}
        onEditClick={jest.fn()}
        onDeleteClick={jest.fn()}
      />
    );
   
    expect(await screen.findByText("rate1")).toBeInTheDocument();
    expect(await screen.findByText("A0")).toBeInTheDocument();
    expect(await screen.findByText("1.1")).toBeInTheDocument();
    expect(await screen.findByText("g1")).toBeInTheDocument();

    expect(await screen.findByText("rate4")).toBeInTheDocument();
    expect(await screen.findByText("A3")).toBeInTheDocument();
    expect(await screen.findByText("4.1")).toBeInTheDocument();
    expect(await screen.findByText("g2")).toBeInTheDocument();
      
  });

  it("should call edit handler when edit button is clicked", async () => {
   
    const handleEditClick = jest.fn();
    render(
      <HotelRatesMappingTable
        hotelRatesList={mockRateList.filter(x=>x.rateid ===1)}
        accessGroups={moockAccessGroups}
        onEditClick={handleEditClick}
        onDeleteClick={jest.fn()}
      />
    );

    await waitFor(() =>{
      const button = screen.getByRole("button", { name: /Edit/i });
      fireEvent.click(button);
      expect(handleEditClick).toHaveBeenCalledWith(1);  
     })
  });

  it("should call delete handler when delete button is clicked", async () => {
   
    const handleDeletClick = jest.fn();
    render(
      <HotelRatesMappingTable
        hotelRatesList={mockRateList.filter(x=>x.rateid ===2)}
        accessGroups={moockAccessGroups}
        onEditClick={jest.fn()}
        onDeleteClick={handleDeletClick}
      />
    );
    await waitFor(() =>{
      const button = screen.getByRole("button", { name: /Delete/i });
      fireEvent.click(button);
      expect(handleDeletClick).toHaveBeenCalledWith(2);  
     })
  });
});