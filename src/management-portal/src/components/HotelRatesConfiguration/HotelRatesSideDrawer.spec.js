import React from "react";
import {
  render,
  screen,
  waitFor,
  fireEvent,
  within,
} from "@testing-library/react";
import HotelRatesSideDrawer from "./HotelRatesSideDrawer";
import { act } from "react-dom/test-utils";

const moockAccessGroups = [
  { accessGroupID: "12341", name: "g1" },
  { accessGroupID: "12342", name: "g2" },
];
describe("render form input element for user input", () => {
  it("should render input element for user inputs", async () => {
    render(
      <HotelRatesSideDrawer
        hotelRate={{}}
        accessGroups={moockAccessGroups}
        handleSave={jest.fn()}
        handleCancel={jest.fn()}
        drawerOpen={true}
      />
    );

    await waitFor(() => {
      expect(
        screen.getByRole("heading", { name: /add \/ edit/i })
      ).toBeInTheDocument();

      expect(
        screen.getByRole("textbox", { name: /name/i })
      ).toBeInTheDocument();

      expect(
        screen.getByRole("textbox", { name: /rate code/i })
      ).toBeInTheDocument();
      expect(
        screen.getByRole("spinbutton", { name: /nightly fee/i })
      ).toBeInTheDocument();

      expect(screen.getByRole("button", { name: /save/i })).toBeInTheDocument();

      expect(
        screen.getByRole("button", { name: /cancel/i })
      ).toBeInTheDocument();
    });
  });
});

describe("from submit tests", () => {
  it("should submit form with valid input", async () => {
    const handleSaveClick = jest.fn();
    render(
      <HotelRatesSideDrawer
        hotelRate={{}}
        accessGroups={moockAccessGroups}
        handleSave={handleSaveClick}
        handleCancel={jest.fn()}
        drawerOpen={true}
      />
    );

    let input = await screen.findByLabelText(/name */i);
    fireEvent.change(input, { target: { value: "rate1" } });

    input = await screen.findByLabelText(/rate code */i);
    fireEvent.change(input, { target: { value: "A0" } });

    input = await screen.findByLabelText(/nightly fee */i);
    fireEvent.change(input, { target: { value: 10.5 } });
    input = await screen.findByLabelText(/access group */i);
    fireEvent.mouseDown(input);
    const listbox = await screen.findByRole("listbox");
    fireEvent.click(within(listbox).getByText(/g1/i));

    const saveButton = await screen.findByRole("button", { name: /save/i });
    fireEvent.click(saveButton);

    await waitFor(() =>
      expect(handleSaveClick).toHaveBeenCalledWith({
        rateid: undefined,
        name: "rate1",
        ratecode: "A0",
        fee: 10.5,
        accessgroupid: "12341",
      })
    );
  });

  it("should not submit form", async () => {
    const handleSaveClick = jest.fn();
    render(
      <HotelRatesSideDrawer
        hotelRate={{}}
        accessGroups={moockAccessGroups}
        handleSave={handleSaveClick}
        handleCancel={jest.fn()}
        drawerOpen={true}
      />
    );
    const saveButton = await screen.findByRole("button", { name: /save/i });
    await fireEvent.click(saveButton);
    expect(handleSaveClick).not.toHaveBeenCalled();
  });

  it("should cancel from", async () => {
    const handleCancelClick = jest.fn();
    render(
      <HotelRatesSideDrawer
        hotelRate={{}}
        accessGroups={moockAccessGroups}
        handleSave={jest.fn()}
        handleCancel={handleCancelClick}
        drawerOpen={true}
      />
    );
    const saveButton = await screen.findByRole("button", { name: /cancel/i });
    await fireEvent.click(saveButton);
    await waitFor(() => expect(handleCancelClick).toHaveBeenCalled());
  });
});

describe("Form validation test", () => {
  it("should render name field is required", async () => {
    render(
      <HotelRatesSideDrawer
        hotelRate={{}}
        accessGroups={moockAccessGroups}
        handleSave={jest.fn()}
        handleCancel={jest.fn()}
        drawerOpen={true}
      />
    );

    await act(async () => {
      const input = screen.getByLabelText(/name */i);
      fireEvent.change(input, { target: { value: "" } });
      const saveButton = screen.getByRole("button", { name: /save/i });
      fireEvent.click(saveButton);
    });
    expect(await screen.findAllByText(/required/i)).toHaveLength(2);
  });

  it("should render name field must be 20 characters or less", async () => {
    render(
      <HotelRatesSideDrawer
        hotelRate={{}}
        accessGroups={moockAccessGroups}
        handleSave={jest.fn()}
        handleCancel={jest.fn()}
        drawerOpen={true}
      />
    );

    await act(async () => {
      const input = screen.getByLabelText(/name */i);
      fireEvent.change(input, { target: { value: "012345678901234567890" } });
      const saveButton = screen.getByRole("button", { name: /save/i });
      fireEvent.click(saveButton);
    });
    expect(
      await screen.findByText(/Name must be 20 characters or less/i)
    ).toBeInTheDocument();
  });

  it("should render rate code field is required", async () => {
    render(
      <HotelRatesSideDrawer
        hotelRate={{}}
        accessGroups={moockAccessGroups}
        handleSave={jest.fn()}
        handleCancel={jest.fn()}
        drawerOpen={true}
      />
    );
    await act(async () => {
      const input = screen.getByLabelText(/rate code */i);
      fireEvent.change(input, { target: { value: "" } });
      const saveButton = screen.getByRole("button", { name: /save/i });
      fireEvent.click(saveButton);
    });
    expect(await screen.findAllByText(/required/i)).toHaveLength(2);
  });

  it("should render rate code field must be 3 characters or less", async () => {
    render(
      <HotelRatesSideDrawer
        hotelRate={{}}
        accessGroups={moockAccessGroups}
        handleSave={jest.fn()}
        handleCancel={jest.fn()}
        drawerOpen={true}
      />
    );

    await act(async () => {
      const input = screen.getByLabelText(/rate code */i);
      fireEvent.change(input, { target: { value: "1234" } });
      const saveButton = screen.getByRole("button", { name: /save/i });
      fireEvent.click(saveButton);
    });
    expect(
      await screen.findByText(/Rate Code must be 3 characters or less/i)
    ).toBeInTheDocument();
  });

  it("should render fee can not be negative", async () => {
    render(
      <HotelRatesSideDrawer
        hotelRate={{}}
        accessGroups={moockAccessGroups}
        handleSave={jest.fn()}
        handleCancel={jest.fn()}
        drawerOpen={true}
      />
    );
    await act(async () => {
      const input = screen.getByLabelText(/nightly fee */i);
      fireEvent.change(input, { target: { value: -1 } });
      const saveButton = screen.getByRole("button", { name: /save/i });
      fireEvent.click(saveButton);
    });

    expect(
      await screen.findByText(/Nightly Fee can not be negative/i)
    ).toBeInTheDocument();
  });

  it("should render fee field must be less than 100000", async () => {
    render(
      <HotelRatesSideDrawer
        hotelRate={{}}
        accessGroups={moockAccessGroups}
        handleSave={jest.fn()}
        handleCancel={jest.fn()}
        drawerOpen={true}
      />
    );

    await act(async () => {
      const input = screen.getByLabelText(/nightly fee */i);
      fireEvent.change(input, { target: { value: "100000" } });
      const saveButton = screen.getByRole("button", { name: /save/i });
      fireEvent.click(saveButton);
    });

    expect(
      await screen.findByText(/Nightly Fee must be less than 100000/i)
    ).toBeInTheDocument();
  });
});
