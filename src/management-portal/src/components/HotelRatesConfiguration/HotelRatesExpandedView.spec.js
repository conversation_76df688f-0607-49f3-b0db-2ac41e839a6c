import React from "react";
import { render, screen, waitFor, fireEvent } from "@testing-library/react";
import HotelRatesExpandedView from "./HotelRatesExpandedView";

import { useSnackbar } from "notistack";
import { useConfirmationDialog } from "../../hooks/useConfirmationDialog";
import AccessGroupService from "../../services/AccessGroupService";
import { useSelector } from "react-redux";

jest.mock("../../hooks/useConfirmationDialog");
jest.mock("../../hooks/useEnqueueSnackbar")
jest.mock("notistack");

jest.mock("../Forms/Settings/index", () => ({
  useSettingDispatchChange: () => jest.fn(),
}));
jest.mock("../../hooks/useCurrentFacility", () =>
  jest.fn().mockReturnValue({ facilityID: "1" })
);
jest.mock("../../services/AccessGroupService", () => jest.fn());
jest.mock("../../auth/apiClient")

jest.mock("../Forms/Settings/index", () => ({
  useSettingDispatchChange: () => jest.fn(),
}));
jest.mock("react-redux", () => ({
  ...jest.requireActual("react-redux"),
  useSelector: jest.fn(() => {
    return {
      entities: {
        Context: {
          settings: {
            preferences: {
              settingname: "test",
            },
          },
        },
      },
    };
  }),
}));
const mockStoreStateForFG = {
  entities: {
    ContextID: 1,
  },
  entityScope: {
    facilityGroupId : undefined,
  },
  user: {
    EmailAddress: "test.com",
},
};

describe("render expanded view", () => {

  beforeEach(() => {
    
    useSnackbar.mockReturnValue({
      enqueueSnackbar: jest.fn(),
      closeSnackbar: jest.fn(),
    });
    useConfirmationDialog.mockReturnValue({
      openDialog: jest.fn(),
      openTextConfirmationDialog: jest.fn(),
    });

    AccessGroupService.prototype.GetAccessGroups = jest
      .fn()
      .mockImplementation(() =>
        Promise.resolve({
          status: 200,
          data: {
            collection: [
              { accessGroupID: "1", name: "AG1" , type:"Normal"},
              { accessGroupID: "2", name: "AG2",type:"Normal" },
            ],
            totalCount:2,
          },
          
        })
      );
    useSelector.mockImplementation((callback) => callback(mockStoreStateForFG));
  });
  
  it("should render expanded view", async () => {
    render(
      <HotelRatesExpandedView
        entityID={"1"}
        hotelInterfaceSettings={"{}"}
        onClose={jest.fn()}
      />
    );
    expect(
      await screen.findByRole("button", { name: /close/i })
    ).toBeInTheDocument();
  });

  it("should call close handler", async () => {
    const onCloseHandler = jest.fn();
    render(
      <HotelRatesExpandedView
        entityID={"1"}
        hotelInterfaceSettings={"{}"}
        onClose={onCloseHandler}
      />
    );

    const button = await screen.findByRole("button", { name: /close/i });
    fireEvent.click(button);
    await waitFor(() => {
      expect(onCloseHandler).toHaveBeenCalledTimes(1);
    });
  });
});
