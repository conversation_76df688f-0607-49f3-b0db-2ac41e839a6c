import React from "react";
import clsx from "clsx";
import moment from "moment-timezone";
import Moment from "react-moment";
import { useStyles } from "./ValetRequestMonitorTable.styles";
import useCurrentFacilityTimezone from "../../../hooks/useCurrentFacilityTimezone";
import CheckCircleIcon from "@material-ui/icons/CheckCircle";

const ValetRequestMonitorTable = ({ results = [] }) => {
  const classes = useStyles();
  const { timeZone } = useCurrentFacilityTimezone();

  return (
    <div className={classes.tableContainer}>
      <table className={clsx(classes.resultTable)}>
        <thead className={clsx(classes.thHeader)}>
          <tr>
          <th className={clsx([classes.resultHeader])}>Ticket Number</th>
          <th className={clsx([classes.centerHeader])}>Wait Time (Mins)</th>
          <th className={clsx([classes.centerHeader])}>Payment Status</th>
          <th className={clsx([classes.centerHeader])}>License Plate</th>
          <th className={clsx([classes.centerHeader])}>Valet Area</th>
          <th className={clsx([classes.centerHeader])}>Parked Space</th>
          <th className={clsx([classes.centerHeader])}>Customer</th>
          <th className={clsx([classes.centerHeader])}>Retrieval Employee</th>
          </tr>
        </thead>

        <tbody>
          {results.map((result) => {
            const requestedTime = moment.utc(result.requestedDate).tz(timeZone);

            return (
            <tr
              key={result.ticketNumber}
              data-testid="requestmonitor-row"
            >
              <td
                data-column="Ticket Number"
                className={clsx([classes.column])}
              >
                  {result.ticketNumber}
                </td>
              <td
                data-column="Wait Time"
                className={clsx([classes.column, classes.centerAlign])}
              >
                  <Moment
                    style={{ color: "inherit" }}
                    date={requestedTime}
                    durationFromNow
                    interval={60000} // Update every minute
                    format="m"
                    unit="minutes"
                  />
                </td>
              <td
                data-column="Payment Status"
                className={clsx([classes.column, classes.centerAlign])}
              >
                <CheckCircleIcon className={clsx([classes.checkIcon])} />
                </td>
              <td
                data-column="License Plate"
                className={clsx([classes.column, classes.centerAlign])}
              >
                  {result.licensePlate}
                </td>
              <td
                data-column="Valet Area"
                className={clsx([classes.column, classes.centerAlign])}
              >
                  {result.entityName}
                </td>
              <td
                data-column="Parked Space"
                className={clsx([classes.column, classes.centerAlign])}
              >
                  {result.parkedSpace}
                </td>
              <td
                data-column="Customer"
                className={clsx([classes.column, classes.centerAlign])}
              >
                  {[result.firstName, result.lastName].filter(Boolean).join(" ")}
                </td>
              <td
                data-column="Retrieval Employee"
                className={clsx([classes.column, classes.centerAlign])}
              >
                  {result.retrievedDate ? "unassigned" : ""}
                </td>
              </tr>
            );
          })}
        </tbody>
      </table>
    </div>
  );
};

export default ValetRequestMonitorTable;
