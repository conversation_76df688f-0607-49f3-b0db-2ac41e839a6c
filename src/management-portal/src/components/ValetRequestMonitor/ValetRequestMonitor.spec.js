import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import ValetRequestMonitor from "./ValetRequestMonitor";
import { useSelector } from "react-redux";
import {
  createMuiTheme,
  ThemeProvider,
  lighten,
  darken,
} from "@material-ui/core";
import { useEnqueueSnackbar } from "../../hooks/useEnqueueSnackbar";
import useCurrentFacility from "../../hooks/useCurrentFacility";
import { act } from "react-dom/test-utils";

jest.mock("react-redux", () => ({
  ...jest.requireActual("react-redux"),
  useSelector: jest.fn(),
}));

jest.mock("../../hooks/useEnqueueSnackbar");
jest.mock("../../hooks/useCurrentFacility");

jest.mock("./ValetRequestMonitor.styles", () => ({
  useStyles: jest.fn(() => ({
    root: "root-class",
    header: "header-class",
    title: "title-class",
    loadingWrapper: "loading-class",
    skeleton: "skeleton-class",
    resultsDivider: "divider-class",
    noResults: "no-results-class",
    pagination: "pagination-class",
    tableContainer: "table-container-class",
  })),
}));

jest.mock("@material-ui/core/styles", () => ({
  ...jest.requireActual("@material-ui/core/styles"),
  lighten: jest.fn(() => "#A3C9F1"),
  darken: jest.fn(() => "#B24A4A"),
}));

jest.mock("../../services/TreeService", () => {
  return jest.fn().mockImplementation(() => {
    return {
      getTreeStructure: jest.fn().mockResolvedValue({
        data: [
          {
            children: [
              { entitytype: "ValetArea", entityname: "Area 1", entityid: "1" },
              { entitytype: "ValetArea", entityname: "Area 2", entityid: "2" },
            ],
          },
        ],
      }),
    };
  });
});

const MockTheme = ({ children }) => {
  const theme = createMuiTheme({
    palette: {
      amano: {
        base: {
          primary: {
            main: "rgb(0, 107, 166)",
          },
          secondary: {
            main: "rgb(0, 107, 166)",
            light: lighten("rgb(0, 107, 166)", 0.2),
          },
        },
        warnings: {
          fire: darken("#FE626B", 0.3),
        },
      },
    },
  });
  return <ThemeProvider theme={theme}>{children}</ThemeProvider>;
};

const mockEntityID = "12345";
const mockEntityName = "Area 1";

const mockRequests = [
  {
    ticketNumber: "12345",
    requestedDate: new Date().toISOString(),
    licensePlate: "ABC123",
    entityName: "Area 1",
    parkedSpace: "Space 42",
    firstName: "John",
    lastName: "Doe",
    retrievedDate: new Date().toISOString(),
    entityID: mockEntityID,
  },
  {
    ticketNumber: "12346",
    requestedDate: new Date().toISOString(),
    licensePlate: "ABC124",
    entityName: "Area 2",
    parkedSpace: "Space 43",
    firstName: "John1",
    lastName: "Doe1",
    retrievedDate: new Date().toISOString(),
    entityID: mockEntityID,
  },
];

describe("ValetRequestMonitor Component", () => {
  const activeSearch = {
    removeRequestAfter: 30,
    requestScope: "All",
    numberOfRequests: 25,
  };

  beforeEach(() => {
    // Correctly mock requestFullscreen and exitFullscreen
    Object.defineProperty(document.documentElement, "requestFullscreen", {
      writable: true,
      value: jest.fn(),
    });
    Object.defineProperty(document, "exitFullscreen", {
      writable: true,
      value: jest.fn(),
    });

    useEnqueueSnackbar.mockReturnValue({
      enqueueSnackbar: jest.fn(),
    });
    useCurrentFacility.mockReturnValue({
      facilityID: mockEntityID,
      facilityName: mockEntityName,
    });
    useSelector.mockImplementation((callback) =>
      callback({
        requestMonitor: {
          requests: mockRequests,
          activeSearch,
        },
        entityScope: {
          facilityGroupId: "1234",
        },
        entities: {
          Context: {
            details: {
              timezone: "America/New_York",
            },
          },
        },
      })
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("renders table and displays with no rows", async () => {
    useSelector.mockImplementation((callback) =>
      callback({
        requestMonitor: {
          requests: [],
          activeSearch,
        },
        entityScope: {
          facilityGroupId: "1234",
        },
        entities: {
          Context: {
            details: {
              timezone: "America/New_York",
            },
          },
        },
      })
    );

    render(
      <MockTheme>
        <ValetRequestMonitor />
      </MockTheme>
    );

    expect(screen.getByText("Request Monitor")).toBeInTheDocument();
    expect(screen.getByText("No active requests")).toBeInTheDocument();
  });

  it("renders the table headers correctly", () => {
    render(
      <MockTheme>
        <ValetRequestMonitor />
      </MockTheme>
    );

    expect(screen.getByText("Ticket Number")).toBeInTheDocument();
    expect(screen.getByText("Wait Time (Mins)")).toBeInTheDocument();
    expect(screen.getByText("Payment Status")).toBeInTheDocument();
    expect(screen.getByText("License Plate")).toBeInTheDocument();
    expect(screen.getByText("Valet Area")).toBeInTheDocument();
    expect(screen.getByText("Parked Space")).toBeInTheDocument();
    expect(screen.getByText("Customer")).toBeInTheDocument();
    expect(screen.getByText("Retrieval Employee")).toBeInTheDocument();
  });

  it("renders rows with provided results data", async () => {
    render(
      <MockTheme>
        <ValetRequestMonitor />
      </MockTheme>
    );

    // Check if data is rendered correctly
    const rows = screen.getAllByTestId("requestmonitor-row");
    expect(rows).toHaveLength(2);
    expect(screen.getByText("12345")).toBeInTheDocument();
    expect(screen.getByText("ABC123")).toBeInTheDocument();
    expect(screen.getByText("Area 1")).toBeInTheDocument();
    expect(screen.getByText("Space 42")).toBeInTheDocument();
    expect(screen.getByText("John Doe")).toBeInTheDocument();
  });

  it("renders search component and applies search", async () => {
    render(
      <MockTheme>
        <ValetRequestMonitor />
      </MockTheme>
    );

    // Check if search component is rendered correctly
    expect(screen.getByText("Remove Request After")).toBeInTheDocument();
    expect(screen.getByText("Request Scope")).toBeInTheDocument();
    expect(screen.getByText("Number of Requests")).toBeInTheDocument();
    expect(screen.getByText("APPLY")).toBeInTheDocument();

    // Simulate search apply
    await act(async () => {
      fireEvent.click(screen.getByText("APPLY"));
    });
  });

  it("renders fullscreen icon when not in fullscreen mode", () => {
    render(
      <MockTheme>
        <ValetRequestMonitor />
      </MockTheme>
    );

    const fullscreenIcon = screen.getByTestId("fullscreen-toggle-icon");
    expect(fullscreenIcon).toBeInTheDocument();
  });

  it("renders exit fullscreen icon when in fullscreen mode", async () => {
    render(
      <MockTheme>
        <ValetRequestMonitor />
      </MockTheme>
    );

    // Simulate fullscreen mode
    const fullscreenIcon = screen.getByTestId("fullscreen-toggle-icon");
    await act(async () => {
      fireEvent.click(fullscreenIcon);
    });

    const exitFullscreenIcon = screen.getByTestId("fullscreen-toggle-icon");
    expect(exitFullscreenIcon).toBeInTheDocument();
  });

  it("opens a new window when fullscreen icon is clicked", async () => {
    // Mock window.open
    const mockWindowOpen = jest.fn();
    window.open = mockWindowOpen;

    render(
      <MockTheme>
        <ValetRequestMonitor />
      </MockTheme>
    );

    const fullscreenIcon = screen.getByTestId("fullscreen-toggle-icon");
    await act(async () => {
      fireEvent.click(fullscreenIcon);
    });

    expect(mockWindowOpen).toHaveBeenCalledWith(
      expect.stringContaining("?poppedOut=true"), // Ensure the URL contains the poppedOut query parameter
      "newWindow",
      "toolbar=no,menubar=no,scrollbars=yes,resizable=yes,width=800,height=600,location=no,noopener,noreferrer"
    );
  });  
});
