import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import ValetRequestMonitorSearch from "./ValetRequestMonitorSearch";
import { REQUEST_MONITOR_REMOVE_REQUEST_TYPE } from "../../../constants";
import { useSelector } from "react-redux";

// Mocking react-redux
jest.mock("react-redux", () => ({
    ...jest.requireActual("react-redux"),
    useSelector: jest.fn(),
}));

// Mocking SelectableChip component
jest.mock("../../SelectableChip", () => {
    const SelectableChip = ({ id, className, variant, text, uppercase, selected, onClick, ...props }) => {
        const handleChipClick = () => {
            if (onClick) {
                onClick(id);
            }
        };

        return (
            <button
                {...props}
                className={className}
                onClick={handleChipClick}
                data-selected={selected ? "true" : "false"}
            >
                {uppercase ? text.toUpperCase() : text}
            </button>
        );
    };

    SelectableChip.displayName = "SelectableChip";

    return {
        __esModule: true,
        default: SelectableChip,
    };
});

describe("ValetRequestMonitorSearch Component", () => {
    const mockOnApply = jest.fn();
    const mockRequestScopeOptions = [
        { id: "1", name: "Area 1" },
        { id: "2", name: "Area 2" },
    ];
    const mockCurrentSelectedEntity = "1";
    const mockCanEditRequestMonitor = true;

    beforeEach(() => {
        useSelector.mockImplementation((callback) =>
            callback({
                requestMonitor: {
                    requests: [],
                },
                entityScope: {
                    facilityGroupId: "1234",
                },
            })
        );
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    it("renders all UI components correctly", () => {
        render(
            <ValetRequestMonitorSearch
                onApply={mockOnApply}
                loading={false}
                requestScopeOptions={mockRequestScopeOptions}
                currentSelectedEntity={mockCurrentSelectedEntity}
                canEditRequestMonitor={mockCanEditRequestMonitor}
            />
        );

        expect(screen.getByText("Remove Request After")).toBeInTheDocument();
        expect(screen.getByText("Request Scope")).toBeInTheDocument();
        expect(screen.getByText("Number of Requests")).toBeInTheDocument();
        expect(screen.getByText("APPLY")).toBeInTheDocument();
    });

    it("handles Remove Request After chip selection correctly", () => {
        render(
            <ValetRequestMonitorSearch
                onApply={mockOnApply}
                loading={false}
                requestScopeOptions={mockRequestScopeOptions}
                currentSelectedEntity={mockCurrentSelectedEntity}
                canEditRequestMonitor={mockCanEditRequestMonitor}
            />
        );

        fireEvent.click(screen.getByText("Retrieval"));
        expect(screen.getByText("Retrieval")).toHaveAttribute("data-selected", "true");

        fireEvent.click(screen.getByText("Delivery"));
        expect(screen.getByText("Delivery")).toHaveAttribute("data-selected", "true");
    });

    it("handles Request Scope chip selection correctly", () => {
        render(
            <ValetRequestMonitorSearch
                onApply={mockOnApply}
                loading={false}
                requestScopeOptions={mockRequestScopeOptions}
                currentSelectedEntity={mockCurrentSelectedEntity}
                canEditRequestMonitor={mockCanEditRequestMonitor}
            />
        );

        expect(screen.getByText("Area 1")).toHaveAttribute("data-selected", "true");

        fireEvent.click(screen.getByText("Area 1"));
        expect(screen.getByText("Area 1")).toHaveAttribute("data-selected", "false");

        fireEvent.click(screen.getByText("Area 2"));
        expect(screen.getByText("Area 2")).toHaveAttribute("data-selected", "true");

        fireEvent.click(screen.getByText("All"));
        expect(screen.getByText("All")).toHaveAttribute("data-selected", "true");
    });

    it("handles the Apply button click correctly", () => {
        render(
            <ValetRequestMonitorSearch
                onApply={mockOnApply}
                loading={false}
                requestScopeOptions={mockRequestScopeOptions}
                currentSelectedEntity={mockCurrentSelectedEntity}
                canEditRequestMonitor={mockCanEditRequestMonitor}
            />
        );

        fireEvent.click(screen.getByText("APPLY"));
        expect(mockOnApply).toHaveBeenCalledWith({
            removeRequestAfter: REQUEST_MONITOR_REMOVE_REQUEST_TYPE.DELIVERY,
            requestScope: ["1"],
            numberOfRequests: 25,
        });
    });

    it("disables UI elements when canEditRequestMonitor is false", () => {
        render(
            <ValetRequestMonitorSearch
                onApply={mockOnApply}
                loading={false}
                requestScopeOptions={mockRequestScopeOptions}
                currentSelectedEntity={mockCurrentSelectedEntity}
                canEditRequestMonitor={false}
            />
        );

        expect(screen.getByText("Retrieval")).toBeDisabled();
        expect(screen.getByText("Delivery")).toBeDisabled();
        expect(screen.getByText("Area 1")).toBeDisabled();
        expect(screen.getByText("Area 2")).toBeDisabled();
        expect(screen.getByRole("button", { name: "APPLY" })).toBeDisabled();
    });
});
