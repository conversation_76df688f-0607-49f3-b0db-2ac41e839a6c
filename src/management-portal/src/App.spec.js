import React from "react";
import { shallow } from "enzyme";
import App, { isGuid } from "./App";
import LoginPage from "./pages/Login";
import AnalyticsPage from "./pages/Analytics";
import KioskMaintenancePage from "./pages/KioskMaintenance";
import KioskManagementPage from "./pages/KioskManagement";
import { RateManagement } from "./pages/Rates";
import SupportPage from "./pages/Support";
import CredentialInventoryPage from "./pages/Monitoring/Inventory";
import UserManagementPage from "./pages/UserManagement";
import ValidationAccountsPage from "./pages/ValidationAccounts";
import Layout from "./components/Layout";
import { Redirect } from "react-router-dom";
import { CssBaseline } from "@material-ui/core";
import useAuthContext from "./hooks/useAuthContext";
import useOnePass from "./hooks/useOnePass/useOnePass";
import { EMPTY_GUID } from "./constants";
import { useFeatureFlag } from "./hooks/useFeatureFlags";
import { useFlags } from "launchdarkly-react-client-sdk";
import RequestMonitorPage from "./pages/Monitoring/RequestMonitor/RequestMonitor";
import ReportQueuePage from "./pages/ReportQueue/ReportQueue";

jest.mock("./hooks/useAuthContext");
jest.mock("launchdarkly-react-client-sdk");
jest.mock("./hooks/useFeatureFlags");
jest.mock("./hooks/useOnePass/useOnePass");

describe("App", () => {
  let facilityID = 123;
  let wrapper;
  beforeEach(() => {
    jest.clearAllMocks();
    useAuthContext.mockReturnValue({
      authReducer: [
        {
          currentFacility: {
            facilityID,
          },
        },
      ],
    });
    useFeatureFlag.mockReturnValue(false);
    useFlags.mockReturnValue({
      systemOffline: {
        enabled: false,
      }
    });
    useOnePass.mockReturnValue({
      isOnePassEnabled: true,
    });
    wrapper = shallow(<App />);
  });

  it("should render without exploding", () => {
    expect(wrapper.exists()).toBe(true);
  });

  it("should render a layout component", () => {
    expect(wrapper.find(Layout).exists()).toBe(true);
  });

  it("should render a CSS Baseline component", () => {
    expect(wrapper.find(CssBaseline).exists()).toBe(true);
  });

  it("should render a route for the login page", () => {
    expect(
      wrapper.findWhere((route) => route.props().path === "/login").exists()
    ).toBe(true);
    expect(wrapper.find(LoginPage).exists()).toBe(true);
  });

  it("should render a protected route for analytics", () => {
    const route = wrapper.findWhere(
      (route) => route.props().path === "/analytics"
    );
    expect(route.props().component).toBe(AnalyticsPage);
  });

  it("should render a protected route for the Kiosk Maintenance Page", () => {
    const route = wrapper.findWhere(
      (route) => route.props().path === "/kioskMaintenance"
    );
    expect(route.props().component).toBe(KioskMaintenancePage);
  });

  it("should render a protected route for the kiosk Management Page", () => {
    const route = wrapper.findWhere(
      (route) => route.props().path === "/kioskManagement"
    );
    expect(route.props().component).toBe(KioskManagementPage);
  });

  it("should render a protected route for the rates page", () => {
    const route = wrapper.findWhere((route) => route.props().path === "/rates");
    expect(route.props().component).toBe(RateManagement);
  });

  it("should render a protected route for the support page", () => {
    const route = wrapper.findWhere(
      (route) => route.props().path === "/support"
    );
    expect(route.props().component).toBe(SupportPage);
  });

  it("should render a protected route for the inventory page", () => {
    const route = wrapper.findWhere(
      (route) => route.props().path === "/inventory"
    );
    expect(route.props().component).toBe(CredentialInventoryPage);
  });

  it("should render a protected route for the valet request monitor page", () => {
    const route = wrapper.findWhere(
      (route) => route.props().path === "/requestmonitor"
    );
    expect(route.props().component).toBe(RequestMonitorPage);
    expect(route.props().permissions).toEqual(["requestmonitor.view"]);
  }); 
  
   it("should render a protected route for the report queue page", () => {
    const route = wrapper.findWhere(
      (route) => route.props().path === "/reportqueue"
    );
    expect(route.props().component).toBe(ReportQueuePage);
    expect(route.props().permissions).toEqual(["reportqueue.view"]);
  });  

  it("should render a protected route for the user management page", () => {
    const route = wrapper.findWhere((route) => route.props().path === "/users");
    expect(route.props().component).toBe(UserManagementPage);
  });

  it("should render a protected route for the validation accounts page", () => {
    const route = wrapper.findWhere(
      (route) => route.props().path === "/validationaccounts"
    );
    expect(route.props().component).toBe(ValidationAccountsPage);
  });

  it("should return true for empty guid", () => {
    expect(isGuid(EMPTY_GUID)).toBe(true);
  });
  it("should return false for non guid", () => {
    expect(isGuid("notguid")).toBe(false);
  });
});

describe("App unauthenticated", () => {
  let wrapper;
  beforeEach(() => {
    jest.clearAllMocks();
    useAuthContext.mockReturnValue({
      authReducer: [
        {
          authStatus: "UNAUTHENTICATED",
        },
      ],
    });
    useFlags.mockReturnValue({
      systemOffline: {
        enabled: false,
      }
    });
    wrapper = shallow(<App />);
  });

  it("should return login page when unauthenticated", () => {
    const redirect = wrapper.find(Redirect);
    expect(redirect.props().from).toBe("/");
    expect(redirect.props().to).toBe(`/login`);
  });
});

describe("App offline", () => {
  let facilityID = 123;
  let wrapper;
  beforeEach(() => {
    jest.clearAllMocks();
    useAuthContext.mockReturnValue({
      authReducer: [
        {
          currentFacility: {
            facilityID,
          },
        },
      ],
    });

    useFlags.mockReturnValue({
      systemOffline: {
        enabled: true,
        message: "system offline"
      }
    });

    wrapper = shallow(<App />);
  });

  it("should return offline page when offline", () => {
    const redirect = wrapper.find(Redirect);
    expect(redirect.props().from).toBe("/");
    expect(redirect.props().to).toBe(`/offline`);
  });
});