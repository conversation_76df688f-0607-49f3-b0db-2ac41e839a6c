// jest-dom adds custom jest matchers for asserting on DOM nodes.
// allows you to do things like:
// expect(element).toHaveTextContent(/react/i)
// learn more: https://github.com/testing-library/jest-dom
import "@testing-library/jest-dom/extend-expect";
import { configure } from "enzyme";
import Adapter from "enzyme-adapter-react-16";
import useHubContext from "./hooks/useHubContext";
jest.mock("./components/Reports/index");
jest.mock("./hooks/usePortalHub/usePortalHubConnectionManager", () =>
  jest.fn().mockReturnValue({})
);
jest.mock("./hooks/useHubContext");
useHubContext.mockReturnValue({
  portalHub: {
    subscribe: jest.fn(),
    invoke: jest.fn(),
  },
});


configure({ adapter: new Adapter() });
