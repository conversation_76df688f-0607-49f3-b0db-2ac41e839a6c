FROM node:20 as react-build

WORKDIR /app
RUN npm install -g npm@9.7.2
ENV NODE_OPTIONS=--max_old_space_size=8192

COPY package*.json ./
COPY .npmrc ./
RUN npm ci

# Second Stage
# Using Alpine images to curb down the image size
FROM node:20 as release
WORKDIR /app
ENV NODE_OPTIONS=--max_old_space_size=8192
COPY --from=react-build /app ./
COPY . ./
RUN npm run build:local

# Stage 3 - the production environment
FROM nginx:alpine
COPY nginx.conf /etc/nginx/conf.d/default.conf
COPY --from=release /app/build /usr/share/nginx/html
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
