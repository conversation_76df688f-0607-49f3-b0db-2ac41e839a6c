Account Number,First Name,Last Name,Company,Address Line 1,Address line 2,City,State,Zip,phone,email,Status,Mode,Start Date,End date,Credential 1 type,Credential 1 number,Credential 1 system code,Credential 2 type,Credential 2 number,Credential 2 system code,Credential 3 type,Credential 3 number,Credential 3 system code,Access group 1,Access group 2,Access group 3,Access group 4,Access group 5,Access group 6,Access group 7,Access group 8,Access group 9,Access group 10,Vehicle 1 make,Vehicle 1 model,Vehicle 1 year,Vehicle 1 color,Vehicle 1 lpn,Vehicle 1 LPN as credential,Vehicle 2 make,Vehicle 2 model,Vehicle 2 year,Vehicle 2 color,Vehicle 2 lpn,Vehicle 2 LPN as credential
12345,<PERSON>,McF<PERSON>,,9303 Lyon Drive,,Hill Valley,CA,95420,************,,neutral,normal,,,Prox,123456,21,,,,,,,Example Access Group Name,,,,,,,,,,DeLorean,DMC-12,1982,Silver,outatime,1,,,,,,0
5431,<PERSON>,<PERSON>,MyCompany,2342 13th Street,,Paris,France,54321,************,<EMAIL>,neutral,normal,,,Prox,123545,42,,,,,,,Example Access Group Name,,,,,,,,,,Metro,Geo,1992,Red,speed,1,,,,,,0
