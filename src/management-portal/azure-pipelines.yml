name: $(Build.SourceBranchName)$(Rev:#r)

trigger:
  branches:
    include:
      - master
      - develop
  paths:
    include:
      - src/management-portal/*
      - pipelines/node/*

pool:
  vmImage: "ubuntu-22.04"

variables:
  isMaster: $[eq(variables['Build.SourceBranch'], 'refs/heads/master')]
  isDev: $[eq(variables['Build.SourceBranch'], 'refs/heads/develop')]
  portalDirectory: src/management-portal
  npm_config_cache: $(Pipeline.Workspace)/.npm
  NPM_CACHE_HIT: "false"
  version: "1.0"
  revision: $[counter(variables['version'], 1)] # This will get reset every time version changes.
  PORTAL_VERSION: "$(version).$(revision)"

stages:
  - stage: test
    displayName: "Run Tests"
    jobs:
      - job: runJestTests
        displayName: "Run Tests"
        steps:
          - template: /pipelines/node/npm-ci-cached.yml
          - bash: "npm run test:ci"
            displayName: "npm run test:ci"
            workingDirectory: $(portalDirectory)

  - stage: deployDev
    displayName: Deploy to Dev
    dependsOn: test
    condition: AND(succeeded(), eq(variables.isDev, true))
    variables:
      - group: "a1-cloud.ALL"
      - group: "DR.RegionSpecific.ALL" #Replaces any region specific vars in case of DR
      - group: "a1-cloud.ENV.DEV" #Needed for BUCKET_NAME, COGNITO_USER_POOL_ID, COGNITO_CLIENT_ID, COGNITO_MOBILE_PAY_AUTH_PROVIDER, COGNITO_MOBILE_PAY_CLIENT_ID, COGNITO_MOBILE_PAY_CLIENT_SECRET, LAUNCH_DARKLY_CLIENT_ID
      - group: "Dev_TurnServer_Vars" #Needed For STUN_URL, TURN_URL, TURN_USERNAME, TURN_PASSWORD
      - group: "DR.RegionSpecific.Dev" #Replaces any region specific vars in case of DR
    jobs:
      - deployment: Portal_Deploy
        environment: "Cloud-Dev"
        displayName: "Build & Deploy to S3"
        strategy:
          runOnce:
            deploy:
              steps:
                - checkout: self
                - template: /pipelines/node/npm-ci-cached.yml
                - template: /pipelines/node/deploy-env-write.yml
                - template: /pipelines/node/deploy-npm-build.yml
                  parameters:
                    buildEnvironment: "dev"
                - template: /pipelines/node/deploy-to-s3.yml
                  parameters:
                    bucketName: $(BUCKET_NAME)
                    awsRegion: $(AWS_REGION)
                    awsCredentials: "AWS-Credentials-DEV"
                - template: /pipelines/node/deploy-notification.yml
                  parameters:
                    NotificationDomain: dev-internal-api.ensemble-cloud.net
                    AzureVendorSecret: $(AZURE_SECRET)

  - stage: deployQA
    displayName: Deploy to QA
    dependsOn: deployDev
    condition: AND(succeeded(), eq(variables.isDev, true))
    variables:
      - group: "a1-cloud.ALL"
      - group: "a1-cloud.ENV.QA"
      - group: "QA_TurnServer_Vars"
      - group: "DR.RegionSpecific.ALL" #Needed just for AWS_REGION
      - group: "DR.RegionSpecific.QA" #NReplaces any region specific vars in case of DR
    jobs:
      - deployment: Portal_Deploy
        environment: "Cloud-QA"
        displayName: "Build & Deploy to S3"
        strategy:
          runOnce:
            deploy:
              steps:
                - checkout: self
                - template: /pipelines/node/npm-ci-cached.yml
                - template: /pipelines/node/deploy-env-write.yml
                - template: /pipelines/node/deploy-npm-build.yml
                  parameters:
                    buildEnvironment: "qa"
                - template: /pipelines/node/deploy-to-s3.yml
                  parameters:
                    bucketName: $(BUCKET_NAME)
                    awsRegion: $(AWS_REGION)
                    awsCredentials: "AWS-Credentials-QA"
                - template: /pipelines/node/deploy-notification.yml
                  parameters:
                    NotificationDomain: qa-internal-api.ensemble-cloud.net
                    AzureVendorSecret: $(AZURE_SECRET)

  - stage: deployStage
    displayName: Deploy to Stage
    dependsOn: test
    condition: AND(succeeded(), eq(variables.isMaster, true))
    variables:
      - group: "a1-cloud.ALL"
      - group: "a1-cloud.ENV.Stage"
      - group: "Stage_TurnServer_Vars"
      - group: "DR.RegionSpecific.ALL" #Needed just for AWS_REGION
      - group: "DR.RegionSpecific.Stage" #Replaces any region specific vars in case of DR
    jobs:
      - deployment: Portal_Deploy
        environment: "Cloud-Stage"
        displayName: "Build & Deploy to S3"
        strategy:
          runOnce:
            deploy:
              steps:
                - checkout: self
                - template: /pipelines/node/npm-ci-cached.yml
                - template: /pipelines/node/deploy-env-write.yml
                - template: /pipelines/node/deploy-npm-build.yml
                  parameters:
                    buildEnvironment: "stage"
                - template: /pipelines/node/deploy-to-s3.yml
                  parameters:
                    bucketName: $(BUCKET_NAME)
                    awsRegion: $(AWS_REGION)
                    awsCredentials: "AWS-Credentials-STAGE"
                - template: /pipelines/node/deploy-notification.yml
                  parameters:
                    NotificationDomain: stage-internal-api.ensemble-cloud.net
                    AzureVendorSecret: $(AZURE_SECRET)

  - stage: deployProduction
    displayName: Deploy to Production
    dependsOn: deployStage
    condition: AND(succeeded(), eq(variables.isMaster, true))
    variables:
      - group: "a1-cloud.ALL"
      - group: "a1-cloud.ENV.Prod"
      - group: "Production_TurnServer_Vars"
      - group: "DR.RegionSpecific.ALL" #Needed just for AWS_REGION
      - group: "DR.RegionSpecific.Prod" #Replaces any region specific vars in case of DR
    jobs:
      - deployment: Portal_Deploy
        environment: "Cloud-Production"
        displayName: "Build & Deploy to S3"
        strategy:
          runOnce:
            deploy:
              steps:
                - checkout: self
                - template: /pipelines/node/npm-ci-cached.yml
                - template: /pipelines/node/deploy-env-write.yml
                - template: /pipelines/node/deploy-npm-build.yml
                  parameters:
                    buildEnvironment: "prod"
                - template: /pipelines/node/deploy-to-s3.yml
                  parameters:
                    bucketName: $(BUCKET_NAME)
                    awsRegion: $(AWS_REGION)
                    awsCredentials: "AWS-Credentials-PROD"
                - template: /pipelines/node/deploy-notification.yml
                  parameters:
                    NotificationDomain: internal-api.amano-one.com
                    AzureVendorSecret: $(AZURE_SECRET)
