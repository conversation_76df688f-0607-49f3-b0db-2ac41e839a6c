<!DOCTYPE html>
<html>
<head>
	<title>HelloWorld.js</title>
</head>
<body>
	<script>var require = function(){return module.exports;}, module={};</script>
	<script src="../src/program.js"></script>
	<script src="../src/statement.js"></script>
	<script src="../src/expression.js"></script>
	<script src="../src/block.js"></script>
	<script src="../src/literal.js"></script>
	<script src="../src/operator.js"></script>
	<script src="../src/variable.js"></script>
	<script src="../src/property.js"></script>
	<script src="../src/assignment.js"></script>
	<script src="../src/condition.js"></script>
	<script src="../src/function.js"></script>
	<script src="../src/call.js"></script>
	<script src="helloworld.js"></script>
</body>
</html>